<template>
  <div class="container">
    <!-- Toast通知组件 -->
    <div v-if="toast.show" class="toast" :class="toast.type">
      {{ toast.message }}
    </div>
    
    <div class="card">
      <h2>{{ $t('config.alarmRules.title') }}</h2>
      <form class="config-form" @submit.prevent="saveAiConfig">
        <div v-if="isLoading" class="loading-spinner">{{ $t('common.loading') }}</div>
        <div v-else class="config-grid">
          <div 
            v-for="(className, classId) in modelClasses" 
            :key="classId" 
            class="config-card" 
            v-show="aiConfig[className]"
          >
            <div class="config-header">
              <span class="class-name">{{ className }}</span>
            </div>
            <div class="config-controls">
              <div class="control-group">
                <label class="control-label">
                  <input 
                    type="checkbox" 
                    v-model="aiConfig[className].enabled"
                    class="control-checkbox"
                  />
                  {{ $t('config.alarmRules.enabled') }}
                </label>
              </div>
              <div class="control-group">
                <label class="control-label">
                  {{ $t('config.alarmRules.confidence') }}:
                  <input 
                    type="number" 
                    v-model="aiConfig[className].confidence"
                    min="0"
                    max="1"
                    step="0.01"
                    :disabled="!aiConfig[className].enabled"
                    class="control-input"
                  />
                </label>
              </div>
            </div>
          </div>
        </div>
        <div class="button-container">
          <button type="submit" :disabled="isLoading">{{ $t('common.save') }}</button>
        </div>
      </form>
    </div>

    <div class="card">
      <h2>{{ $t('config.storage.title') }}</h2>
      <form class="config-form" @submit.prevent="saveStorageConfig">
        <ul>
          <li>
            <label>
              {{ $t('config.storage.imagePath') }}：
              <input
                type="text"
                v-model="storageConfig.imagePath"
                :placeholder="$t('config.storage.imagePath')"
                class="storage-path-input"
              />
              <span class="field-hint">{{ $t('config.storage.pathHint') }}</span>
            </label>
          </li>
          <li>
            <label>
              {{ $t('config.storage.videoPath') }}：
              <input
                type="text"
                v-model="storageConfig.videoPath"
                :placeholder="$t('config.storage.videoPath')"
                class="storage-path-input"
              />
              <span class="field-hint">{{ $t('config.storage.videoHint') }}</span>
            </label>
          </li>
          <li>
            <label>
              {{ $t('config.storage.retentionDays') }}：
              <select v-model="storageConfig.retentionDays">
                <option value="7">{{ $t('config.storage.days', { count: 7 }) }}</option>
                <option value="15">{{ $t('config.storage.days', { count: 15 }) }}</option>
                <option value="30">{{ $t('config.storage.days', { count: 30 }) }}</option>
                <option value="60">{{ $t('config.storage.days', { count: 60 }) }}</option>
                <option value="90">{{ $t('config.storage.days', { count: 90 }) }}</option>
              </select>
            </label>
          </li>
          <li>
            <label>
              {{ $t('config.storage.autoCleanup') }}：
              <select v-model="storageConfig.autoCleanup">
                <option value="enabled">{{ $t('config.storage.enabled') }}</option>
                <option value="disabled">{{ $t('config.storage.disabled') }}</option>
              </select>
            </label>
          </li>
        </ul>
        <div class="button-container">
          <button type="submit">{{ $t('common.save') }}</button>
          <button type="button" @click="testStoragePath">{{ $t('config.storage.testPath') }}</button>
        </div>
      </form>
    </div>

    <div class="card">
      <h2>{{ $t('config.email.title') }}</h2>
      <form class="config-form" @submit.prevent="saveEmailConfig">
        <ul>
          <li>
            <label
              >{{ $t('config.email.dailyEmail') }}：
              <select v-model="emailConfig.enabled">
                <option value="enabled">{{ $t('config.storage.enabled') }}</option>
                <option value="disabled">{{ $t('config.storage.disabled') }}</option>
              </select>
            </label>
          </li>
          <li>
            <label
              >{{ $t('config.email.sendTime') }}：
              <select v-model="emailConfig.sendTime">
                <option value="18:00">18:00</option>
                <option value="19:00">19:00</option>
                <option value="20:00">20:00</option>
                <option value="21:00">21:00</option>
              </select>
            </label>
          </li>
          <li>
            <label
              >{{ $t('config.email.recipients') }}：
              <input
                type="text"
                v-model="emailConfig.recipients"
                :placeholder="$t('config.email.recipientsHint')"
              />
            </label>
          </li>
          <li>
            <label
              >{{ $t('config.email.subject') }}：
              <input
                type="text"
                v-model="emailConfig.subject"
                :placeholder="$t('config.email.subjectHint')"
              />
            </label>
          </li>
          <li>
            <label
              >{{ $t('config.email.content') }}：
              <textarea
                v-model="emailConfig.content"
                :placeholder="$t('config.email.contentHint')"
                class="email-content-textarea"
                rows="8"
              ></textarea>
            </label>
          </li>
        </ul>

        <div class="button-container">
          <button type="submit">{{ $t('config.email.saveConfig') }}</button>
          <button type="button" @click="fetchTodayAlarms">{{ $t('config.email.refreshData') }}</button>
          <button type="button" @click="testEmail">{{ $t('config.email.sendTest') }}</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import emailjs from '@emailjs/browser'
import { getModelClasses, getAiConfig, updateAiConfig, type AiConfig, type ModelClasses } from '@/services/aiConfigService';
import { ElSwitch, ElSlider } from 'element-plus';

const { t: $t } = useI18n()

// --- AI Config State ---
const modelClasses = ref<ModelClasses>({});
const aiConfig = ref<AiConfig>({});
const isLoading = ref(true);

const storageConfig = ref({
  imagePath: '/storage/images',
  videoPath: '/storage/videos',
  retentionDays: '30',
  autoCleanup: 'enabled'
})

// Toast通知状态
const toast = ref({
  show: false,
  message: '',
  type: 'info' as 'info' | 'success' | 'error'
})

// 显示Toast通知
function showToast(message: string, type: 'info' | 'success' | 'error' = 'info') {
  toast.value = {
    show: true,
    message,
    type
  }
  
  // 3秒后自动关闭
  setTimeout(() => {
    toast.value.show = false
  }, 3000)
}

const emailConfig = ref({
  enabled: 'enabled',
  sendTime: '18:00',
  recipients: '<EMAIL>, <EMAIL>',
  subject: '智能监控系统 - 今日报警总结',
  content: '正在加载今日报警数据...'
})

// --- AI Config Methods ---
async function loadAiConfigData() {
  isLoading.value = true;
  try {
    const [classes, config] = await Promise.all([
      getModelClasses(),
      getAiConfig()
    ]);
    modelClasses.value = classes;
    aiConfig.value = config;
    showToast($t('messages.configLoaded'), 'success');
  } catch (error) {
    console.error("加载AI配置失败:", error);
    showToast($t('messages.configLoadFailed'), 'error');
  } finally {
    isLoading.value = false;
  }
}

async function saveAiConfig() {
  isLoading.value = true;
  try {
    await updateAiConfig(aiConfig.value);
    showToast($t('messages.configSaved'), 'success');
  } catch (error) {
    console.error("保存AI配置失败:", error);
    showToast($t('messages.configSaveFailed'), 'error');
  } finally {
    isLoading.value = false;
  }
}

function saveStorageConfig() {
  console.log('Saving storage config:', storageConfig.value)
  showToast($t('messages.configSaved'), 'success')
}

function testStoragePath() {
  console.log(
    'Testing storage paths:',
    storageConfig.value.imagePath,
    storageConfig.value.videoPath
  )
  showToast($t('messages.testingStoragePath'), 'info')
  setTimeout(() => {
    showToast($t('messages.storageTestPassed'), 'success')
  }, 1000)
}

function saveEmailConfig() {
  console.log('Saving email config:', emailConfig.value)
  showToast($t('messages.emailConfigSaved'), 'success')
}

// 获取今日alarms数据并生成邮件内容
async function fetchTodayAlarms() {
  try {
    const response = await fetch('http://localhost:8000/api/alarms/')
    if (response.ok) {
      const alarms = await response.json()
      
      // 获取今日日期
      const today = new Date()
      const todayStr = today.toISOString().split('T')[0] // YYYY-MM-DD格式
      
      // 过滤今日的报警记录
      const todayAlarms = alarms.filter((alarm: any) => {
        const alarmDate = alarm.time.split(' ')[0] // 提取日期部分
        return alarmDate === todayStr
      })
      
      // 统计各类型报警数量
      const typeStats: { [key: string]: number } = {}
      todayAlarms.forEach((alarm: any) => {
        typeStats[alarm.type] = (typeStats[alarm.type] || 0) + 1
      })
      
      // 生成邮件内容
      let emailContent = $t('emailTemplate.greeting', { 
        date: `${today.getFullYear()}年${String(today.getMonth() + 1).padStart(2, '0')}月${String(today.getDate()).padStart(2, '0')}日` 
      })

      emailContent += `\n\n${$t('emailTemplate.statistics')}`
      emailContent += `\n${$t('emailTemplate.totalAlarms', { count: todayAlarms.length })}`

      // 添加各类型统计
      Object.entries(typeStats).forEach(([type, count]) => {
        emailContent += `\n${$t('emailTemplate.typeStats', { type, count })}`
      })
      
      emailContent += `\n\n${$t('emailTemplate.details')}`
      
      // 添加详细记录
      todayAlarms.forEach((alarm: any, index: number) => {
        emailContent += `\n${$t('emailTemplate.recordFormat', { 
          index: index + 1, 
          time: alarm.time, 
          type: alarm.type, 
          location: alarm.location 
        })}`
        if (alarm.screenshot) {
          emailContent += `   ${$t('emailTemplate.imageLink', { url: alarm.screenshot })}`
        }
      })
      
      emailConfig.value.content = emailContent
      showToast($t('messages.dataRefreshSuccess'), 'success')
    } else {
      console.error('获取报警数据失败:', response.statusText)
      emailConfig.value.content = $t('messages.dataRefreshFailed')
    }
  } catch (error) {
    console.error('获取报警数据出错:', error)
    emailConfig.value.content = $t('messages.networkError')
  }
}

// 组件挂载时获取数据
onMounted(() => {
  loadAiConfigData();
  fetchTodayAlarms();
})

function testEmail() {
  const serviceId = import.meta.env.VITE_EMAILJS_SERVICE_ID
  const templateId = import.meta.env.VITE_EMAILJS_TEMPLATE_ID
  const publicKey = import.meta.env.VITE_EMAILJS_PUBLIC_KEY

  if (!serviceId || !templateId || !publicKey) {
    showToast($t('messages.emailjsConfigIncomplete'), 'error')
    return
  }

  showToast($t('messages.sendingTestEmail'), 'info')

  emailjs.send(serviceId, templateId, {
    email_title: emailConfig.value.subject,
    email_content: emailConfig.value.content,
    to_email: emailConfig.value.recipients
  }, publicKey)
    .then((response) => {
      console.log('✅ 邮件发送成功:', response)
      showToast($t('messages.emailSentSuccess'), 'success')
    })
    .catch((error) => {
      console.error('❌ 邮件发送失败:', error)
      showToast($t('messages.emailSentFailed', { error: error.text || error.message }), 'error')
    })
}
</script>

<style scoped>
.loading-spinner {
  text-align: center;
  padding: 48px;
  font-size: 1.1rem;
  color: #6b7280;
}

/* AI配置样式 */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.config-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.config-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.class-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
}

.config-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.control-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.control-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.95rem;
  color: #374151;
  font-weight: 500;
}

.control-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #3b82f6;
}

.control-input {
  width: 80px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.95rem;
  text-align: center;
}

.control-input:disabled {
  background-color: #f3f4f6;
  color: #9ca3af;
}

.control-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* 存储配置样式 */
.storage-path-input {
  width: 100%;
  max-width: 400px;
  margin-top: 5px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 1rem;
}

.field-hint {
  display: block;
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 4px;
}

/* 邮件配置样式 */
.email-content-textarea {
  width: 100%;
  max-width: 600px;
  margin-top: 5px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-family: inherit;
  font-size: 1rem;
  resize: vertical;
}

/* 通用卡片样式 */
.card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  padding: 24px;
}

.card h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #111827;
  font-size: 1.5rem;
  font-weight: 600;
}

.config-form ul {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.config-form li {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e5e7eb;
}

.config-form li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.config-form label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.config-form input,
.config-form select,
.config-form textarea {
  width: 100%;
  max-width: 400px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 1rem;
}

.config-form input:focus,
.config-form select:focus,
.config-form textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.button-container {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
}

.button-container button {
  padding: 10px 20px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button-container button:hover {
  background-color: #2563eb;
}

.button-container button:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.button-container button[type="button"] {
  background-color: #6b7280;
}

.button-container button[type="button"]:hover {
  background-color: #4b5563;
}

/* Toast通知样式 */
.toast { 
  position: fixed; 
  top: 20px; 
  right: 20px; 
  padding: 12px 20px; 
  border-radius: 8px; 
  color: white; 
  font-weight: 600; 
  z-index: 9999; 
  max-width: 320px; 
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); 
  animation: slideIn 0.3s ease-out; 
}
.toast.info { background-color: #3b82f6; }
.toast.success { background-color: #10b981; }
.toast.error { background-color: #ef4444; }
@keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }
</style>
