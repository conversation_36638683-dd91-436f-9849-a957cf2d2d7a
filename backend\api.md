# 智能视频监控中心服务器 API 文档 (v4.1)

本文档详细说明了中心服务器提供的 RESTful API 端点。架构已升级为服务器主动从摄像头 RTSP 地址拉取视频流进行分析。

## 1. AI 与拉流控制 API

### 1.1. `GET /api/ai/config`

获取当前的全局 AI 检测配置，用于动态调整模型的行为。

-   **成功响应 (200 OK):**
    ```json
    {
      "person": {"enabled": true, "confidence": 0.7},
      "car": {"enabled": true, "confidence": 0.55}
    }
    ```

### 1.2. `PUT /api/ai/config`

更新全局 AI 检测配置。只有在请求体中出现的类别才会被更新。

-   **请求体 (JSON):**
    ```json
    {
      "person": {"confidence": 0.75},
      "car": {"enabled": false}
    }
    ```
-   **成功响应 (204 No Content):** 表示配置已成功更新。

### 1.3. `POST /api/cameras/{camera_id}/start_stream`

手动启动指定摄像头的 RTSP 拉流和 AI 分析任务。

-   **URL 参数:** `camera_id` (integer, required)
-   **成功响应 (200 OK):** `{"message": "摄像头 1 的拉流任务已启动。"}`

### 1.4. `POST /api/cameras/{camera_id}/stop_stream`

手动停止指定摄像头的拉流和 AI 分析任务。

-   **URL 参数:** `camera_id` (integer, required)
-   **成功响应 (200 OK):** `{"message": "摄像头 1 的拉流任务已停止。"}`

---

## 2. CRUD API (数据管理)

### 2.1. 工地区域 (Sites)

-   `POST /api/sites/`: 创建一个新的工地区域。
-   `GET /api/sites/`: 获取所有工地区域的列表。
-   `GET /api/sites/{site_id}`: 获取指定 ID 的工地区域信息。
-   `PUT /api/sites/{site_id}`: 更新指定 ID 的工地区域信息。
-   `DELETE /api/sites/{site_id}`: 删除指定 ID 的工地区域。

### 2.2. 摄像头 (Cameras)

-   `POST /api/cameras/`: 注册一个新的摄像头。
-   `GET /api/cameras/`: 获取所有摄像头的列表。
-   `GET /api/cameras/{camera_id}`: 获取指定 ID 的摄像头信息。
-   `PUT /api/cameras/{camera_id}`: 更新指定 ID 的摄像头信息。
-   `DELETE /api/cameras/{camera_id}`: 删除指定 ID 的摄像头。

### 2.3. 警报 (Alarms)

-   `GET /api/alarms/`: 获取警报列表。
-   `GET /api/alarms/stats/today-by-type`: 获取用于首页图表的今日报警统计数据。

---

## 3. 已移除的旧版 API

-   `POST /ingest/{camera_id}`: 已被服务器主动拉流取代。
-   `POST /api/cameras/{camera_id}/subscribe`: 实时视频流分发逻辑已变更，此 WebRTC 订阅接口已移除。
