from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Enum, Numeric, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
try:
    from database import Base
except ImportError:
    from database import Base
import enum

# Define ENUM types using Python's enum class for consistency
class CameraStatusEnum(enum.Enum):
    在线 = "在线"
    离线 = "离线"
    维修中 = "维修中"

class AlertStatusEnum(enum.Enum):
    未确认 = "未确认"
    已确认 = "已确认"
    误报 = "误报"

class Site(Base):
    __tablename__ = "sites"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, nullable=False)
    area = Column(String)
    description = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    cameras = relationship("Camera", back_populates="site", cascade="all, delete-orphan")

class Camera(Base):
    __tablename__ = "cameras"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    ip_address = Column(String, nullable=False) # IP地址
    port = Column(Integer, nullable=False) # 端口号
    username = Column(String, nullable=True) # 用户名
    password = Column(String, nullable=True) # 密码
    stream_path = Column(String, nullable=True) # 流路径，例如 /Streaming/Channels/101
    rtsp_url = Column(String, nullable=True) # 最终拼接好的RTSP拉流地址
    location = Column(String)
    latitude = Column(Numeric(12, 8), nullable=True)  # 纬度，精度到8位小数
    longitude = Column(Numeric(12, 8), nullable=True)  # 经度，精度到8位小数
    status = Column(Enum(CameraStatusEnum), default=CameraStatusEnum.离线)
    site_id = Column(Integer, ForeignKey("sites.id"))
    last_online = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    site = relationship("Site", back_populates="cameras")
    alerts = relationship("Alert", back_populates="camera")
    presets = relationship("CameraPreset", back_populates="camera", cascade="all, delete-orphan")

    __table_args__ = (UniqueConstraint('ip_address', 'port', name='_ip_port_uc'),)

class Alert(Base):
    __tablename__ = "alerts"

    id = Column(Integer, primary_key=True, index=True)
    alert_type = Column(String, nullable=False)
    alert_time = Column(DateTime(timezone=True), nullable=False)
    camera_id = Column(Integer, ForeignKey("cameras.id"))
    image_path = Column(String)
    status = Column(Enum(AlertStatusEnum), default=AlertStatusEnum.未确认)
    handler = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    camera = relationship("Camera", back_populates="alerts")

class CameraPreset(Base):
    __tablename__ = "camera_presets"

    id = Column(Integer, primary_key=True, index=True)
    camera_id = Column(Integer, ForeignKey("cameras.id", ondelete="CASCADE"), nullable=False)
    name = Column(String, nullable=False)
    pan = Column(Numeric(8, 4), nullable=False)  # Pan position (-180 to 180 degrees)
    tilt = Column(Numeric(8, 4), nullable=False)  # Tilt position (-90 to 90 degrees)
    zoom = Column(Numeric(6, 2), nullable=False, default=1.0)  # Zoom level (1.0 to 100.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    camera = relationship("Camera", back_populates="presets")

# 添加Alarm表（用于前端显示）
class Alarm(Base):
    __tablename__ = "alarms"
    
    id = Column(Integer, primary_key=True, index=True)
    time = Column(String, nullable=False)  # 报警时间
    type = Column(String, nullable=False)  # 报警类型
    location = Column(String, nullable=False)  # 摄像头位置
    screenshot = Column(String)  # 截图路径
    status = Column(String, default="未确认")  # 状态
    handler = Column(String, default="-")  # 处理人
    created_at = Column(DateTime(timezone=True), server_default=func.now())
