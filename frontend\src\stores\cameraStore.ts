import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import * as cameraService from '@/services/cameraService';
import type { Camera, Site, SiteCreate } from '@/services/cameraService';

// Define a new type for creating cameras that doesn't require all fields
export type CameraCreatePayload = Omit<cameraService.Camera, 'id' | 'status' | 'last_online'>;

export const useCameraStore = defineStore('cameras', () => {
  // --- STATE ---
  const sites = ref<Site[]>([]);
  const cameras = ref<Camera[]>([]);
  const isLoading = ref(false);
  const lastFetchTime = ref<number>(0); // 缓存上次获取数据的时间

  // --- GETTERS ---
  const allCameras = computed(() => cameras.value);
  const onlineCameras = computed(() => cameras.value.filter(c => c.status === '在线'));
  const offlineCameras = computed(() => cameras.value.filter(c => c.status !== '在线'));

  const stats = computed(() => ({
    totalSites: sites.value.length,
    totalCameras: cameras.value.length,
    onlineCameras: onlineCameras.value.length,
    offlineCameras: offlineCameras.value.length,
  }));

  const getSiteName = (siteId: number): string => {
    const site = sites.value.find(s => s.id === siteId);
    return site ? site.name : '未知工地';
  };

  // --- ACTIONS ---
  async function fetchAllData(forceRefresh = false) {
    const now = Date.now();
    const cacheValidTime = 30000; // 30秒缓存
    
    // 如果不强制刷新且缓存仍然有效，则跳过
    if (!forceRefresh && 
        (now - lastFetchTime.value) < cacheValidTime && 
        lastFetchTime.value > 0) {
      console.log('[cameraStore] fetchAllData: Using cached data, skipping fetch');
      return;
    }
    
    console.log('[cameraStore] fetchAllData: Starting...');
    if (isLoading.value) {
      console.log('[cameraStore] fetchAllData: Already loading, skipping...');
      return;
    }
    
    isLoading.value = true;
    try {
      await Promise.all([fetchSites(), fetchCameras()]);
      lastFetchTime.value = now;
      console.log('[cameraStore] fetchAllData: All fetches complete.');
    } catch (error) {
      console.error('[cameraStore] fetchAllData: An error occurred.', error);
    } finally {
      isLoading.value = false;
      console.log('[cameraStore] fetchAllData: Finished. isLoading set to false.');
    }
  }

  async function fetchSites() {
    console.log('[cameraStore] fetchSites: Fetching sites...');
    try {
      const data = await cameraService.getSites();
      if (Array.isArray(data)) {
        sites.value = data;
        console.log(`[cameraStore] fetchSites: Success, ${data.length} sites loaded.`);
      } else {
        sites.value = [];
        console.warn("[cameraStore] fetchSites: Did not return an array. Defaulting to empty array.", data);
      }
    } catch (error) {
      console.error('[cameraStore] fetchSites: Error fetching sites:', error);
      sites.value = [];
      throw error; // 重新抛出错误以便上层处理
    }
  }

  async function fetchCameras() {
    console.log('[cameraStore] fetchCameras: Fetching cameras...');
    try {
      const data = await cameraService.getCameras();
      if (Array.isArray(data)) {
        cameras.value = data;
        console.log(`[cameraStore] fetchCameras: Success, ${data.length} cameras loaded.`);
      } else {
        cameras.value = [];
        console.warn("[cameraStore] fetchCameras: Did not return an array. Defaulting to empty array.", data);
      }
    } catch (error) {
      console.error('[cameraStore] fetchCameras: Error fetching cameras:', error);
      cameras.value = [];
      throw error; // 重新抛出错误以便上层处理
    }
  }

  async function addSite(siteData: SiteCreate) {
    try {
      await cameraService.createSite(siteData);
      await fetchSites(); // Refresh the list
      console.log('工地添加成功');
    } catch (error) {
      console.error('添加工地失败:', error);
    }
  }

  async function updateSite(id: number, siteData: Partial<SiteCreate>) {
    try {
      await cameraService.updateSite(id, siteData);
      await fetchSites(); // Refresh the list
      console.log('工地更新成功');
    } catch (error) {
      console.error('更新工地失败:', error);
    }
  }

  async function deleteSite(id: number) {
    try {
      await cameraService.deleteSite(id);
      await fetchAllData(true); // 强制刷新数据
      console.log('工地删除成功');
    } catch (error) {
      console.error('删除工地失败:', error);
    }
  }

  async function addCamera(cameraData: CameraCreatePayload) {
    try {
      console.log('[cameraStore] Adding camera:', cameraData);
      const newCamera = await cameraService.createCamera(cameraData as cameraService.CameraCreate);
      console.log('[cameraStore] Camera created:', newCamera);
      
      // 清除缓存并刷新数据
      lastFetchTime.value = 0; // 清除缓存
      await fetchCameras(); // Refresh the list
      console.log('[cameraStore] 摄像头添加成功，当前摄像头数量:', cameras.value.length);
    } catch (error) {
      console.error('添加摄像头失败:', error);
    }
  }

  async function updateCamera(id: number, cameraData: Partial<CameraCreatePayload>) {
    try {
      await cameraService.updateCamera(id, cameraData as Partial<cameraService.CameraCreate>);
      await fetchCameras(); // Refresh the list
      console.log('摄像头更新成功');
    } catch (error) {
      console.error('更新摄像头失败:', error);
    }
  }

  async function deleteCamera(id: number) {
    try {
      console.log(`[cameraStore] 删除摄像头 ID: ${id}`);
      await cameraService.deleteCamera(id);
      
      // 清除缓存并刷新数据
      lastFetchTime.value = 0; // 清除缓存
      await fetchCameras(); // Refresh the list
      console.log(`[cameraStore] 摄像头删除成功，当前摄像头数量: ${cameras.value.length}`);
    } catch (error) {
      console.error('删除摄像头失败:', error);
    }
  }
  
  async function deleteMultipleCameras(ids: number[]) {
    isLoading.value = true;
    try {
      console.log(`[cameraStore] 批量删除摄像头 IDs:`, ids);
      await Promise.all(ids.map(id => cameraService.deleteCamera(id)));
      
      // 清除缓存并刷新数据
      lastFetchTime.value = 0; // 清除缓存
      await fetchCameras();
      console.log(`[cameraStore] 成功删除 ${ids.length} 个摄像头，当前摄像头数量: ${cameras.value.length}`);
    } catch (error) {
      console.error('批量删除摄像头失败:', error);
    } finally {
      isLoading.value = false;
    }
  }

  return {
    // State
    sites,
    cameras,
    isLoading,
    // Getters
    allCameras,
    onlineCameras,
    offlineCameras,
    stats,
    getSiteName,
    // Actions
    fetchAllData,
    fetchSites,
    fetchCameras,
    addSite,
    updateSite,
    deleteSite,
    addCamera,
    updateCamera,
    deleteCamera,
    deleteMultipleCameras,
  };
});