# 智能视频监控系统 API 文档 (V2)

本文档是前端应用与后端 API 网关交互的官方指南。所有 API 请求都应发往此后端服务。

---

## 1. 云台与视频流 (PTZ & Video Stream)

这些端点负责代理对下游服务（如 RK3568）的实时控制和视频流请求。

### 1.1 获取实时视频流

*   **端点:** `GET /api/ptz/video_feed`
*   **作用:** 从 RK3568 代理和获取处理后的实时视频流。
*   **输入:** 无。
*   **输出:** 视频流 (`multipart/x-mixed-replace` 格式)。
*   **调用示例 (HTML):**
    ```html
    <img src="http://<后端服务器IP>:8000/api/ptz/video_feed">
    ```

### 1.2 物理云台控制

*   **端点:** `GET /api/ptz/control`
*   **作用:** 代理物理云台的移动命令到 RK3568，最终由 ESP32 执行。
*   **输入:** 查询参数 (Query Parameters)
    *   `pan` (int): 水平平移角度。
    *   `tilt` (int): 垂直倾斜角度。
*   **输出:** 确认操作结果的 JSON 对象。
*   **调用示例:**
    ```bash
    curl "http://<后端服务器IP>:8000/api/ptz/control?pan=90&tilt=45"
    ```

### 1.3 数字云台控制 (点击放大)

*   **端点:** `GET /api/ptz/zoom_to_point`
*   **作用:** 代理数字云台 (DPTZ) 的缩放命令到 RK3568，最终由树莓派执行。
*   **输入:** 查询参数 (Query Parameters)
    *   `level` (float): 缩放级别 (>= 1.0)。
    *   `x` (int): 点击位置的 X 坐标。
    *   `y` (int): 点击位置的 Y 坐标。
    *   `frame_width` (int): 前端画面的宽度。
    *   `frame_height` (int): 前端画面的高度。
*   **输出:** 确认操作结果的 JSON 对象。
*   **调用示例:**
    ```bash
    curl "http://<后端服务器IP>:8000/api/ptz/zoom_to_point?level=2.5&x=640&y=360&frame_width=1280&frame_height=720"
    ```

---

## 2. 工地区域管理 (Sites)

### 2.1 创建工地区域

*   **端点:** `POST /sites/`
*   **作用:** 创建一个新的工地区域。
*   **输入:** 请求体 (Request Body)
    ```json
    {
      "name": "雄安新区一号工地",
      "location": "河北省雄安新区"
    }
    ```
*   **输出:** 创建成功后的工地区域对象。

### 2.2 获取工地区域列表

*   **端点:** `GET /sites/`
*   **作用:** 获取所有工地区域的列表。
*   **输入:** 无。
*   **输出:** 包含多个工地区域对象的数组。

### 2.3 获取单个工地区域

*   **端点:** `GET /sites/{site_id}`
*   **作用:** 根据 ID 获取单个工地区域的详细信息。
*   **输入:** 路径参数 `site_id` (int)。
*   **输出:** 单个工地区域对象。

---

## 3. 摄像头管理 (Cameras)

### 3.1 创建摄像头

*   **端点:** `POST /cameras/`
*   **作用:** 在指定的工地区域下创建一个新的摄像头。
*   **输入:** 请求体 (Request Body)
    ```json
    {
      "name": "1号门入口摄像头",
      "rtsp_url": "rtsp://...",
      "status": "online",
      "site_id": 1
    }
    ```
*   **输出:** 创建成功后的摄像头对象。

### 3.2 获取摄像头列表

*   **端点:** `GET /cameras/`
*   **作用:** 获取所有摄像头的列表。
*   **输入:** 无。
*   **输出:** 包含多个摄像头对象的数组。

### 3.3 获取单个摄像头

*   **端点:** `GET /cameras/{camera_id}`
*   **作用:** 根据 ID 获取单个摄像头的详细信息。
*   **输入:** 路径参数 `camera_id` (int)。
*   **输出:** 单个摄像头对象。

---

## 4. 警报管理 (Alerts)

### 4.1 创建警报

*   **端点:** `POST /alerts/`
*   **作用:** 为指定的摄像头创建一个新的警报记录。
*   **输入:** 请求体 (Request Body)
    ```json
    {
      "timestamp": "2025-08-13T10:30:00Z",
      "alert_type": "安全帽未佩戴",
      "image_url": "/alerts/images/alert_123.jpg",
      "camera_id": 1
    }
    ```
*   **输出:** 创建成功后的警报对象。

### 4.2 获取警报列表

*   **端点:** `GET /alerts/`
*   **作用:** 获取所有警报的列表。
*   **输入:** 无。
*   **输出:** 包含多个警报对象的数组。

### 4.3 获取单个警报

*   **端点:** `GET /alerts/{alert_id}`
*   **作用:** 根据 ID 获取单个警报的详细信息。
*   **输入:** 路径参数 `alert_id` (int)。
*   **输出:** 单个警报对象。
