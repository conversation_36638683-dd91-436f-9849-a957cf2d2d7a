import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    port: 8007, // 指定一个新的端口号
    // 允许外部访问（关键配置）
    host: '0.0.0.0',  // 监听所有网络接口
    
    // 允许访问的域名（关键配置）
    // allowedHosts: ['11209vovl6353.vicp.fun'],  // 替换为您的花生壳域名
    
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8000', // 后端服务器地址
        changeOrigin: true,
        secure: false,
        ws: true,
        timeout: 10000,
        // 保持/api前缀，因为后端API端点都有/api前缀
        // rewrite: (path) => path.replace(/^\/api/, '')
      },
      // '/raw_video_feed': {
      //   target: 'http://11209vovl6353.vicp.fun',
      //   changeOrigin: true,
      // },
    }
  }
})

