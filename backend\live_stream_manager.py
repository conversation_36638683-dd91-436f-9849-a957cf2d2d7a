# backend/live_stream_manager.py

import asyncio
import logging
import subprocess
import threading
from typing import Dict
from pathlib import Path

from config import settings
from ai_processor import AIProcessor

logger = logging.getLogger(__name__)

class LiveStreamManager:
    def __init__(self, ai_processor: AIProcessor):
        self.ai_processor = ai_processor
        self.active_streams: Dict[int, threading.Thread] = {}
        self.viewer_counts: Dict[int, int] = {}
        self.hls_output_dir = Path(settings.OUTPUT_DIR) / "live_hls"
        self.hls_output_dir.mkdir(parents=True, exist_ok=True)
        self._lock = threading.Lock()
        self._stop_events: Dict[int, threading.Event] = {}

    async def get_stream_url(self, camera_id: int) -> str:
        with self._lock:
            self.viewer_counts[camera_id] = self.viewer_counts.get(camera_id, 0) + 1
            logger.info(f"摄像头 {camera_id}: 观众+1, 当前总数: {self.viewer_counts[camera_id]}")

            if camera_id not in self.active_streams or not self.active_streams[camera_id].is_alive():
                logger.info(f"摄像头 {camera_id}: 第一个观众进入，启动LL-HLS编码线程。")
                stop_event = threading.Event()
                loop = asyncio.get_running_loop()
                thread = threading.Thread(target=self._ffmpeg_encoder_thread, args=(camera_id, stop_event, loop))
                self._stop_events[camera_id] = stop_event
                self.active_streams[camera_id] = thread
                thread.start()
            
            return f"/live_hls/{camera_id}/playlist.m3u8"

    async def release_stream_url(self, camera_id: int):
        with self._lock:
            if camera_id in self.viewer_counts:
                self.viewer_counts[camera_id] -= 1
                if self.viewer_counts[camera_id] <= 0:
                    del self.viewer_counts[camera_id]
                    self.stop_stream_process(camera_id)

    def stop_stream_process(self, camera_id: int):
        with self._lock:
            if camera_id in self._stop_events:
                self._stop_events[camera_id].set()
            thread = self.active_streams.pop(camera_id, None)
            if thread and thread.is_alive():
                thread.join(timeout=10)

    def stop_all_streams(self):
        logger.info("正在停止所有LL-HLS生成任务...")
        with self._lock:
            for stop_event in self._stop_events.values():
                stop_event.set()
            for thread in self.active_streams.values():
                if thread.is_alive():
                    thread.join(timeout=10)
        self.active_streams.clear()
        self._stop_events.clear()
        self.viewer_counts.clear()
        logger.info("所有LL-HLS生成任务已停止。")

    def _ffmpeg_encoder_thread(self, camera_id: int, stop_event: threading.Event, loop: asyncio.AbstractEventLoop):
        queue = self.ai_processor.subscribe(camera_id)
        process = None
        try:
            future = asyncio.run_coroutine_threadsafe(queue.get(), loop)
            first_frame = future.result(timeout=15)
            height, width, _ = first_frame.shape

            camera_hls_dir = self.hls_output_dir / str(camera_id)
            playlist_path = camera_hls_dir / "playlist.m3u8"

            ffmpeg_cmd = [
                'ffmpeg', '-y',
                '-f', 'rawvideo', '-vcodec', 'rawvideo',
                '-s', f'{width}x{height}', '-pix_fmt', 'bgr24',
                '-r', str(settings.VIDEO_FPS),
                '-i', '-',
                '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency',
                '-an', # 无音频
                '-f', 'hls', '-hls_time', '2', '-hls_list_size', '5',
                '-hls_flags', 'delete_segments+program_date_time',
                str(playlist_path)
            ]

            process = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            process.stdin.write(first_frame.tobytes())

            while not stop_event.is_set():
                future = asyncio.run_coroutine_threadsafe(queue.get(), loop)
                frame = future.result(timeout=5)
                process.stdin.write(frame.tobytes())

        except (asyncio.TimeoutError, queue.Empty):
            logger.error(f"LL-HLS编码线程 {camera_id}: 从AI处理器获取帧超时。")
        except Exception as e:
            logger.error(f"LL-HLS编码线程 {camera_id} 发生错误: {e}", exc_info=True)
        finally:
            self.ai_processor.unsubscribe(camera_id, queue)
            if process: process.terminate()
            logger.info(f"LL-HLS编码线程 {camera_id} 已清理资源。")
