# backend/main.py

import asyncio
import logging
from contextlib import asynccontextmanager
from typing import Dict, Any, List

from fastapi import FastAPI, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from pathlib import Path

from ai_config_manager import ai_config_manager
from ai_processor import AIProcessor
from config import settings
import crud, models, schemas
from database import SessionLocal, engine, get_db
from live_stream_manager import LiveStreamManager

# --- Logger Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("main_server")

# --- Background Tasks ---
async def startup_background_tasks(app: FastAPI):
    await asyncio.sleep(0.1)
    logger.info("后台任务：开始启动所有AI分析任务...")
    db = SessionLocal()
    try:
        cameras = crud.get_cameras(db, limit=1000)
        for camera in cameras:
            if camera.rtsp_url:
                app.state.ai_processor.start_analysis_task(camera.id, camera.rtsp_url)
    finally:
        db.close()

# --- FastAPI Lifespan ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("应用启动...")
    app.state.ai_processor = AIProcessor()
    logger.info("✅ AI 处理器已初始化")
    app.state.live_stream_manager = LiveStreamManager(ai_processor=app.state.ai_processor)
    logger.info("✅ 直播流管理器已初始化")
    models.Base.metadata.create_all(bind=engine)
    asyncio.create_task(startup_background_tasks(app))
    yield
    logger.info("应用关闭...")
    if app.state.live_stream_manager: app.state.live_stream_manager.stop_all_streams()
    if app.state.ai_processor: app.state.ai_processor.stop_all_tasks()

# --- FastAPI App Instance ---
app = FastAPI(
    title="智能视频监控中心服务器 API",
    description="通过RTSP主动拉取，按需生成LL-HLS直播流，并提供AI分析功能。",
    version="5.3.1", # Final Corrected Version
    lifespan=lifespan
)

# --- Middleware & Static Files ---
app.add_middleware(CORSMiddleware, allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"])
hls_dir = Path(settings.OUTPUT_DIR) / "live_hls"
hls_dir.mkdir(parents=True, exist_ok=True)
app.mount("/live_hls", StaticFiles(directory=hls_dir), name="live_hls")

# --- API Endpoints ---

# AI & Stream Control
@app.get("/api/ai/config", response_model=Dict[str, Any], tags=["AI & Stream Control"])
async def get_ai_config(): return ai_config_manager.get_all_config()

@app.put("/api/ai/config", status_code=status.HTTP_204_NO_CONTENT, tags=["AI & Stream Control"])
async def update_ai_config(new_config: Dict[str, Any]):
    try: ai_config_manager.update_config(new_config)
    except Exception as e: raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

@app.get("/api/ai/model/classes", response_model=Dict[int, str], tags=["AI & Stream Control"])
async def get_model_classes(request: Request):
    ai_processor = request.app.state.ai_processor
    if not ai_processor or not hasattr(ai_processor, 'model'):
        raise HTTPException(status_code=503, detail="AI处理器尚未初始化")
    return ai_processor.model.names

@app.get("/api/cameras/{camera_id}/live_url", response_model=schemas.StreamURL, tags=["AI & Stream Control"])
async def get_live_stream_url(camera_id: int, request: Request):
    manager = request.app.state.live_stream_manager
    if camera_id not in request.app.state.ai_processor.active_analysis_tasks:
         raise HTTPException(status_code=404, detail="摄像头当前离线或未在处理中")
    url = await manager.get_stream_url(camera_id)
    return {"url": url}

# CRUD: Sites
@app.post("/api/sites/", response_model=schemas.Site, tags=["CRUD - Sites"])
def create_site(site: schemas.SiteCreate, db: Session = Depends(get_db)):
    if crud.get_site_by_name(db, name=site.name): raise HTTPException(status_code=400, detail="该工地名称已存在")
    return crud.create_site(db=db, site=site)

@app.get("/api/sites/", response_model=List[schemas.Site], tags=["CRUD - Sites"])
def read_sites(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)): return crud.get_sites(db, skip=skip, limit=limit)

@app.get("/api/sites/{site_id}", response_model=schemas.Site, tags=["CRUD - Sites"])
def read_site(site_id: int, db: Session = Depends(get_db)):
    db_site = crud.get_site(db, site_id=site_id)
    if db_site is None: raise HTTPException(status_code=404, detail="未找到该工地区域")
    return db_site

@app.put("/api/sites/{site_id}", response_model=schemas.Site, tags=["CRUD - Sites"])
def update_site(site_id: int, site: schemas.SiteCreate, db: Session = Depends(get_db)):
    if crud.get_site(db, site_id=site_id) is None: raise HTTPException(status_code=404, detail="未找到该工地区域")
    return crud.update_site(db=db, site_id=site_id, site=site)

@app.delete("/api/sites/{site_id}", response_model=schemas.Site, tags=["CRUD - Sites"])
def delete_site(site_id: int, db: Session = Depends(get_db)):
    deleted_site = crud.delete_site(db, site_id=site_id)
    if deleted_site is None: raise HTTPException(status_code=404, detail="未找到该工地区域")
    return deleted_site

# CRUD: Cameras
@app.post("/api/cameras/", response_model=schemas.Camera, tags=["CRUD - Cameras"])
def create_camera(camera: schemas.CameraCreate, db: Session = Depends(get_db)):
    try: return crud.create_camera(db=db, camera=camera)
    except ValueError as e: raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/cameras/", response_model=List[schemas.Camera], tags=["CRUD - Cameras"])
def read_cameras(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)): return crud.get_cameras(db, skip=skip, limit=limit)

@app.get("/api/cameras/{camera_id}", response_model=schemas.Camera, tags=["CRUD - Cameras"])
def read_camera(camera_id: int, db: Session = Depends(get_db)):
    db_camera = crud.get_camera(db, camera_id=camera_id)
    if db_camera is None: raise HTTPException(status_code=404, detail="未找到该摄像头")
    return db_camera

@app.put("/api/cameras/{camera_id}", response_model=schemas.Camera, tags=["CRUD - Cameras"])
def update_camera(camera_id: int, camera: schemas.CameraUpdate, db: Session = Depends(get_db)):
    if crud.get_camera(db, camera_id=camera_id) is None: raise HTTPException(status_code=404, detail="未找到该摄像头")
    return crud.update_camera(db=db, camera_id=camera_id, camera=camera)

@app.delete("/api/cameras/{camera_id}", response_model=schemas.Camera, tags=["CRUD - Cameras"])
def delete_camera_endpoint(camera_id: int, db: Session = Depends(get_db)):
    deleted_camera = crud.delete_camera(db, camera_id=camera_id)
    if deleted_camera is None: raise HTTPException(status_code=404, detail="未找到该摄像头")
    return deleted_camera

# CRUD: Alarms
@app.get("/api/alarms/", response_model=List[schemas.Alarm], tags=["CRUD - Alarms"])
def read_alarms(skip: int = 0, limit: int = 100, db: Session = Depends(get_db)): return crud.get_alarms(db, skip=skip, limit=limit)

@app.get("/api/alarms/stats/today-by-type", tags=["CRUD - Alarms"])
def get_alarm_stats_today_by_type(db: Session = Depends(get_db)):
    chart_labels = ["人员禁区", "车辆禁区", "人员入侵", "车辆入侵"]
    stats_dict = crud.get_today_alarms_stats_by_type(db)
    chart_data = [stats_dict.get(label, 0) for label in chart_labels]
    return {"labels": chart_labels, "data": chart_data}

# Serve Frontend
static_files_dir = Path(__file__).parent.parent / "frontend" / "dist"
if static_files_dir.exists():
    app.mount("/", StaticFiles(directory=str(static_files_dir), html=True), name="static-frontend")
