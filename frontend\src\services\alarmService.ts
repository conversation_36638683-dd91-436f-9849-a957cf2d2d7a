// src/services/alarmService.ts
import axios from 'axios';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

export interface Alarm {
  id: number;
  time: string;
  type: string;
  location: string;
  screenshot: string;
  status: '已确认' | '未确认' | '误报';
  handler: string;
}

export interface AlarmStats {
  labels: string[];
  data: number[];
}

export const getAlarms = async (filters: any): Promise<Alarm[]> => {
  try {
    console.log('Fetching alarms with filters:', filters);
    const response = await apiClient.get('/alarms/', { params: filters });
    return response.data;
  } catch (error) {
    console.error('获取告警列表失败:', error);
    return [];
  }
};

export const getTodayAlarmStats = async (): Promise<AlarmStats> => {
  try {
    const response = await apiClient.get<AlarmStats>('/alarms/stats/today-by-type');
    return response.data;
  } catch (error) {
    console.error('获取今日告警统计失败:', error);
    // 返回一个默认的空状态，以防止图表渲染失败
    return { labels: [], data: [] };
  }
};

export const updateAlarmStatus = async (id: number, status: '已确认' | '误报'): Promise<boolean> => {
    try {
        console.log(`Updating alarm ${id} to status ${status}`);
        const response = await apiClient.patch(`/alarms/${id}`, { status });
        console.log('状态更新成功！');
        return response.status === 200;
    } catch (error) {
        console.error('更新告警状态失败:', error);
        return false;
    }
}

