# Gemini AI 驱动的 PTZ 摄像机控制系统

## 1. 项目概述

本项目是一个采用“服务器中心”架构的智能视频监控系统，专为大规模（约100个摄像头）和高性能的AI应用而设计。系统由 AI 驱动，用于 PTZ（平移-倾斜-变焦）摄像机控制。

**核心架构思想:**
数据流遵循 **RK3568 -> 服务器 (AI处理 + 视频转发) -> 前端** 的模式。这种设计的目的是：
- **带宽效率:** 原始视频流仅在边缘设备和服务器之间传输，减少网络拥堵。
- **简化边缘设备:** RK3568 设备只负责视频采集和推流，降低了其硬件和软件复杂性。
- **集中化处理:** 所有AI计算（使用支持批处理的YOLOv11x模型）和业务逻辑都在服务器上执行，便于管理、扩展和利用高性能硬件（如GPU）。

系统由三部分组成：

*   **前端 (PC - 用户界面):** 一个基于 Vue.js 3 和 Vite 的现代化 Web 应用。它提供了用户界面，用于查看来自服务器的、经过AI处理的视频流、发送PTZ控制命令以及管理警报。

*   **后端 (服务器):** 一个基于 Python FastAPI 的高性能后端服务，是整个系统的核心。它负责：
    *   接收来自多达100个RK3568设备的并发视频流。
    *   使用 YOLOv11x 模型对视频帧进行批处理（Batch Processing），以实现高效的AI推理。
    *   将带有AI分析结果的视频流分发给多个前端客户端。
    *   提供 RESTful API 用于管理工地、摄像头和警报数据。
    *   接收前端的PTZ控制请求，并通过海康威视ISAPI协议直接控制摄像头。
    *   使用 SQLAlchemy 与数据库进行交互。

*   **RK3568 视频采集端:** 一个轻量级的边缘设备，其唯一职责是：
    *   通过网线连接到海康威视摄像头。
    *   捕获原始视频流。
    *   使用 WebRTC 将视频流稳定地推送到中心服务器。

## 2. 构建与运行

要运行本系统，您需要按顺序启动后端、前端和 RK3568 服务。

### 2.1. 后端 (FastAPI)

1.  **进入目录:**
    ```bash
    cd front/backend
    ```

2.  **安装依赖:**
    ```bash
    pip install -r requirements.txt
    ```

3.  **运行服务:**
    ```bash
    # 默认在 http://localhost:8000 运行
    uvicorn main:app --host 0.0.0.0 --port 8000
    ```

### 2.2. 前端 (Vue.js)

1.  **进入目录:**
    ```bash
    cd front/frontend
    ```

2.  **安装依赖:**
    ```bash
    npm install
    ```

3.  **为开发环境编译和热重载:**
    ```bash
    npm run dev
    ```

### 2.3. RK3568 视频采集端

1.  **进入目录:**
    ```bash
    cd rk3568
    ```

2.  **安装依赖:**
    ```bash
    pip install -r requirements.txt
    ```

3.  **配置环境变量:**
    在 `rk3568` 目录下创建一个 `.env` 文件，并设置以下变量：
    ```
    VIDEO_SOURCE="rtsp://your_camera_rtsp_url" # 海康威视摄像头的RTSP地址
    CENTRAL_SERVER_URL="http://your_server_ip:8000" # 中心服务器的地址
    CAMERA_ID="camera-001" # 当前摄像头的唯一标识
    ```

4.  **运行服务:**
    ```bash
    python main.py
    ```

## 3. 开发约定

*   **API 驱动的通信:** 前端和后端通过定义良好的 RESTful API 和 WebSocket/WebRTC 进行通信。
*   **前后端分离:** 项目采用完全前后端分离的架构，允许独立开发和部署。
*   **代码风格:**
    *   前端代码遵循 ESLint 和 Prettier 的规则。
    *   后端代码遵循 FastAPI 的最佳实践。
*   **环境配置:**
    *   后端服务的配置（数据库、AI模型等）通过 `front/backend/config.py` 管理。
    *   前端的代理配置在 `front/frontend/vite.config.ts` 中定义。
    *   RK3568 服务的配置通过 `rk3568/.env` 文件管理。
