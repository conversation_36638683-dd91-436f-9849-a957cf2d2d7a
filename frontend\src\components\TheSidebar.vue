<template>
  <aside class="sidebar">
    <header class="sidebar-header">
      <h1>{{ $t('navigation.systemTitle') }}</h1>
    </header>
    <nav>
      <ul>
        <li>
          <router-link to="/" :class="{ active: $route.path === '/' }">
            <span class="icon">🏠</span>{{ $t('navigation.home') }}
          </router-link>
        </li>
        <li>
          <router-link to="/camera-points" :class="{ active: $route.path === '/camera-points' }">
            <span class="icon">📹</span>{{ $t('navigation.cameraPoints') }}
          </router-link>
        </li>
        <li>
          <router-link to="/playback" :class="{ active: $route.path === '/playback' }">
            <span class="icon">🎬</span>{{ $t('navigation.playback') }}
          </router-link>
        </li>
        <li class="dropdown" :class="{ active: isResourceManagementOpen }">
          <a href="#" class="dropdown-toggle" @click.prevent="toggleDropdown" :class="{ active: isResourceManagementOpen }">
            <span class="icon">📁</span>{{ $t('navigation.resourceManagement') }}
          </a>
          <ul class="dropdown-menu" v-show="isResourceManagementOpen">
            <li>
              <router-link to="/alarms" :class="{ active: $route.path === '/alarms' }">
                <span class="icon alarm-bell">🔔</span>{{ $t('navigation.alarmManagement') }}
              </router-link>
            </li>
            <li>
              <router-link to="/management" :class="{ active: $route.path === '/management' }">
                <span class="icon">📹</span>{{ $t('navigation.cameraManagement') }}
              </router-link>
            </li>
          </ul>
        </li>
        <li>
          <router-link to="/config" :class="{ active: $route.path === '/config' }">
            <span class="icon">⚙️</span>{{ $t('navigation.config') }}
          </router-link>
        </li>
      </ul>
    </nav>
    
    <!-- 语言切换器 - 放在导航栏最下方 -->
    <div class="language-switcher-container">
      <LanguageSwitcher />
    </div>
  </aside>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import LanguageSwitcher from './LanguageSwitcher.vue';

const route = useRoute();
const { t: $t } = useI18n();

// Computed property to check if the current route is under resource management
const isResourceManagementRoute = computed(() => {
  return route.path.startsWith('/alarms') || route.path.startsWith('/management');
});

// Ref to control the dropdown's visibility
const isResourceManagementOpen = ref(isResourceManagementRoute.value);

// Function to toggle the dropdown
function toggleDropdown() {
  isResourceManagementOpen.value = !isResourceManagementOpen.value;
}

// Watch for route changes to automatically open the dropdown if navigating to a child route
watch(isResourceManagementRoute, (isChildRoute) => {
  if (isChildRoute) {
    isResourceManagementOpen.value = true;
  }
});
</script>

<style scoped>
/* Using global styles from main.css, no specific styles needed here for now */
.dropdown-toggle.active::after {
    transform: rotate(180deg);
}

/* 语言切换器容器样式 */
.language-switcher-container {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 确保侧边栏有相对定位 */
.sidebar {
  position: relative;
}
</style>
