export default {
  // 通用
  common: {
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    search: '搜索',
    refresh: '刷新',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息'
  },

  // 导航
  navigation: {
    systemTitle: '智能视频监控系统',
    home: '监控主页',
    cameraPoints: '摄像头点位',
    playback: '监控回放',
    resourceManagement: '资源管理',
    alarmManagement: '告警事件管理',
    cameraManagement: '摄像头管理',
    config: '系统配置',
    alarms: '报警记录',
    cameras: '摄像头管理',
    about: '关于系统'
  },

  // 管理页面
  management: {
    pageTitle: '摄像头管理',
    siteManagement: '工地管理',
    cameraManagement: '摄像头管理'
  },

  // 首页
  home: {
    cameraDistribution: '摄像头分布概览',
    latestAlarms: '最新报警信息',
    alarmCount: '报警数量',
    unknownSite: '未知工地',
    alarmDetails: '报警详情',
    alarmDescription: '在 {location} 于 {time} 发生。',
    viewDetails: '查看详情',
    clickToView: '点击查看详情',
    
    // 报警类型
    alarmTypes: {
      personForbidden: '施工人员禁区作业',
      vehicleForbidden: '工程车辆禁区作业',
      personIntrusion: '施工人员违规闯入',
      vehicleIntrusion: '工程车辆违规闯入'
    },
    
    // 摄像头位置
    cameraLocations: {
      materialArea: '2号摄像头：材料区',
      entrance: '1号摄像头：工地入口',
      craneArea: '3号摄像头：塔吊作业区'
    },
    
    // 时间相关
    timeAgo: {
      twoMinutes: '2分钟前',
      fiveMinutes: '5分钟前',
      twelveMinutes: '12分钟前',
      thirtyMinutes: '30分钟前'
    },
    
    // 摄像头信息
    cameraInfo: {
      id: '摄像头ID',
      status: '状态',
      coordinates: '坐标',
      location: '位置'
    },
    
    // 摄像头状态
    cameraStatus: {
      online: '在线',
      offline: '离线'
    },
    
    // 图表标签
    chartLabels: {
      todayAlarms: '今日报警次数',
      fourDayTotal: '近4日报警总量'
    }
  },

  // 摄像头点位页面
  cameraPoints: {
    cameraList: '摄像头列表',
    realTimeVideo: '摄像头实时画面',
    gridMode: '分屏模式',
    previousPage: '上一页',
    nextPage: '下一页',
    pageInfo: '第 {current} / {total} 页',
    restartCamera: '重启摄像头',
    restart: '重启',
    viewPlayback: '查看回放',
    addPreset: '添加预置点',
    readPreset: '读取预置点',
    deleteAllPresets: '删除全部预置点',
    savedPresets: '已存预置点',
    apply: '应用',
    delete: '删除',
    cameraTitle: '{id}号摄像头: {name}',
    applyPresetFailed: '应用预置点失败：无效的角度值。',
    confirmDeletePreset: '确定要删除这个预置点吗?',
    
    // 报警规则配置
    alarmRulesConfig: '报警规则配置',
    personDetection: '识别区域内出现施工人员',
    helmetDetection: '识别人员未佩戴安全帽',
    vehicleDetection: '识别工程车辆',
    
    // 识别区域绘制
    cancelDrawing: '取消绘制',
    setRecognitionArea: '设定/重设识别区域',
    clearCurrentArea: '清除当前区域',
    saveCurrentConfig: '保存当前配置',
    recognitionAreaCompleted: '识别区域绘制完成！\n\n区域已自动闭合，请点击"保存当前配置"来保存设置。',
    drawingCancelled: '已取消绘制，未完成的区域已清除',
    drawingModeEnabled: '绘制模式已开启 - 请在画面上点击定义识别区域',
    pointsDrawn: '已绘制 {count} 个点',
    minimumPointsRequired: '至少需要3个点',
    
    // 远程控制与喊话
    remoteControlAndVoice: '远程控制与喊话',
    inputVoiceContent: '输入喊话内容...',
    send: '发送',
    realTimeIntercom: '实时对讲',
    loopPlayback: '循环播放'
  },

  // 摄像头控制面板
  cameraControl: {
    panTiltOperation: '云台操作',
    reset: '重置',
    zoom: '调焦',
    focus: '聚焦',
    aperture: '光圈',
    zoomLevel: '变倍',
    resetSuccess: '摄像头参数已重置为默认值'
  },

  // 回放页面
  playback: {
    playbackControl: '回放控制',
    selectCamera: '选择摄像头',
    pleaseSelectCamera: '请选择摄像头',
    cameraTitle: '{id}号摄像头：{name}',
    selectTime: '选择时间',
    playbackControls: '回放控制',
    play: '播放',
    pause: '暂停',
    stop: '停止',
    playbackSpeed: '播放速度',
    timeline: '时间轴',
    operations: '操作',
    screenshot: '截图',
    exportClip: '导出片段',
    videoPlayback: '视频回放',
    eventMarkers: '事件标记',
    jump: '跳转',
    nowPlaying: '正在播放...',
    paused: '已暂停',
    pleaseSelectToStart: '请选择摄像头和时间开始回放',
    pleaseSelectCameraFirst: '请先选择摄像头！',
    pleaseSelectDate: '请选择日期！',
    pleaseStartPlaybackFirst: '请先开始播放！',
    screenshotFunction: '截图功能：\n\n已保存当前帧截图\n文件名：{filename}',
    enterExportStartTime: '请输入导出开始时间 (格式: HH:MM:SS):',
    enterExportEndTime: '请输入导出结束时间 (格式: HH:MM:SS):',
    exportFunction: '导出功能：\n\n正在导出视频片段\n时间范围：{startTime} - {endTime}',
    eventTypes: {
      personForbidden: '施工人员禁区作业',
      vehicleForbidden: '工程车辆违规进入',
      noHelmet: '施工人员未佩戴安全帽'
    }
  },

  // 报警记录页面
  alarms: {
    title: '报警记录管理',
    totalCount: '总报警数量',
    todayCount: '今日报警数量',
    status: {
      unconfirmed: '未确认',
      confirmed: '已确认',
      falseAlarm: '误报'
    },
    actions: {
      confirm: '确认报警',
      markFalse: '标记误报',
      edit: '编辑记录',
      delete: '删除记录'
    },
    filters: {
      search: '搜索报警记录...',
      status: '状态筛选',
      type: '类型筛选'
    },
    table: {
      id: 'ID',
      time: '时间',
      type: '类型',
      location: '位置',
      screenshot: '截图',
      status: '状态',
      handler: '处理人',
      actions: '操作'
    }
  },

  // 配置页面
  config: {
    // 报警规则配置
    alarmRules: {
      title: '摄像头全局报警规则配置',
      personDetection: '识别区域内出现施工人员',
      helmetDetection: '识别人员未佩戴安全帽',
      vehicleDetection: '识别工程车辆',
      confidence: '置信度阈值',
      enabled: '启用'
    },

    // 存储配置
    storage: {
      title: '存储位置配置',
      imagePath: '图片存储路径',
      videoPath: '录像存储路径',
      retentionDays: '存储保留天数',
      days: '{count}天',
      autoCleanup: '自动清理',
      enabled: '开启',
      disabled: '关闭',
      testPath: '测试存储路径',
      pathHint: '支持相对/绝对路径，建议使用绝对路径',
      videoHint: '建议使用大容量存储，支持网络路径'
    },

    // 邮件配置
    email: {
      title: '邮件发送配置',
      dailyEmail: '每日邮件发送',
      sendTime: '发送时间',
      recipients: '收件人邮箱',
      subject: '邮件主题',
      content: '邮件内容',
      recipientsHint: '请输入邮箱地址，多个邮箱用逗号分隔',
      subjectHint: '请输入邮件主题',
      contentHint: '请输入邮件内容',
      saveConfig: '保存邮件配置',
      refreshData: '刷新今日数据',
      sendTest: '发送测试邮件'
    }
  },

  // 邮件内容模板
  emailTemplate: {
    greeting: '您好！以下是{date}监控系统的报警总结：',
    statistics: '📊 今日报警统计',
    totalAlarms: '• 总报警数量：{count}条',
    typeStats: '• {type}：{count}条',
    details: '📋 详细报警记录',
    recordFormat: '{index}. {time} {type} {location}',
    imageLink: '图片链接：{url}'
  },

  // 时间相关
  time: {
    today: '今日',
    yesterday: '昨日',
    thisWeek: '本周',
    thisMonth: '本月',
    thisYear: '今年'
  },

  // 状态消息
  messages: {
    configSaved: '配置已保存！',
    storageTestPassed: '✅ 存储路径测试通过！',
    emailConfigSaved: '邮件配置已保存！',
    emailSentSuccess: '✅ 测试邮件发送成功！',
    emailSentFailed: '❌ 测试邮件发送失败: {error}',
    emailjsConfigIncomplete: 'EmailJS 配置信息不完整，请检查 .env.local 文件。',
    sendingTestEmail: '正在发送测试邮件...',
    testingStoragePath: '正在测试存储路径...',
    dataRefreshSuccess: '✅ 今日报警数据加载成功，邮件内容已更新',
    dataRefreshFailed: '获取今日报警数据失败，请检查后端服务',
    networkError: '获取今日报警数据出错，请检查网络连接'
  }
} 