<template>
  <div class="camera-control-panel">
    <div class="control-panel-header">
      <span class="control-icon">⚽</span>
      <span class="control-title">{{ $t('cameraControl.panTiltOperation') }}</span>
      <button class="control-reset-btn" @click="resetControls">{{ $t('cameraControl.reset') }}</button>
    </div>
    <div class="camera-controls">
      <div class="control-row">
        <!-- Direction Control Pad -->
        <div class="direction-control">
          <div class="direction-pad">
            <button class="direction-btn up-left" @click.prevent="move('up-left')">↖</button>
            <button class="direction-btn up" @click.prevent="move('up')">↑</button>
            <button class="direction-btn up-right" @click.prevent="move('up-right')">↗</button>
            <button class="direction-btn left" @click.prevent="move('left')">←</button>
            <button class="direction-btn center" @click.prevent="move('center')">⟲</button>
            <button class="direction-btn right" @click.prevent="move('right')">→</button>
            <button class="direction-btn down-left" @click.prevent="move('down-left')">↙</button>
            <button class="direction-btn down" @click.prevent="move('down')">↓</button>
            <button class="direction-btn down-right" @click.prevent="move('down-right')">↘</button>
          </div>
        </div>
        
        <!-- Parameter Controls -->
        <div class="parameter-controls">
          <div class="parameter-row">
            <span class="parameter-label">{{ $t('cameraControl.zoom') }}</span>
            <div class="parameter-buttons">
              <button class="param-btn plus" @click.prevent="adjustParam('zoom', 'plus')">+</button>
              <button class="param-btn minus" @click.prevent="adjustParam('zoom', 'minus')">-</button>
            </div>
          </div>
          <div class="parameter-row">
            <span class="parameter-label">{{ $t('cameraControl.focus') }}</span>
            <div class="parameter-buttons">
              <button class="param-btn plus" @click.prevent="adjustParam('aperture', 'plus')">+</button>
              <button class="param-btn minus" @click.prevent="adjustParam('aperture', 'minus')">-</button>
            </div>
          </div>
          <div class="parameter-row">
            <span class="parameter-label">{{ $t('cameraControl.aperture') }}</span>
            <div class="parameter-buttons">
              <button class="param-btn plus" @click.prevent="adjustParam('focus', 'plus')">+</button>
              <button class="param-btn minus" @click.prevent="adjustParam('focus', 'minus')">-</button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Speed Control -->
      <div class="speed-control">
        <span class="parameter-label">{{ $t('cameraControl.zoomLevel') }}</span>
        <div class="speed-slider-container">
          <input type="range" id="modal-speed-slider" class="speed-slider" min="1" max="3" v-model="speed">
          <span class="speed-value">{{ speed }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t: $t } = useI18n();

const speed = ref(1);
const isProcessing = ref(false); // 防止重复点击

const emit = defineEmits(['direction']);

// 防抖处理，避免快速点击导致的问题
function move(direction: string) {
  if (isProcessing.value) {
    console.log(`PTZ操作进行中，跳过: ${direction}`);
    return;
  }
  
  isProcessing.value = true;
  console.log(`PTZ移动: ${direction}, 速度: ${speed.value}`);
  
  try {
    emit('direction', direction);
  } finally {
    // 短暂延迟后允许下一次操作
    setTimeout(() => {
      isProcessing.value = false;
    }, 100);
  }
}

function adjustParam(param: string, action: string) {
  console.log(`PTZ调整: ${param} ${action}`);
  // 参数调整暂时只记录日志
}

function resetControls() {
  if (isProcessing.value) {
    console.log('PTZ操作进行中，跳过重置');
    return;
  }
  
  speed.value = 1;
  console.log('PTZ控制重置');
  move('center'); // 重置时发送居中命令
  // 移除alert避免DOM元素创建
  console.log($t('cameraControl.resetSuccess'));
}
</script>

<style scoped>
/* All styles are now global from main.css */
</style>

