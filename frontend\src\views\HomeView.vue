<template>
  <div class="container">
    <div class="dashboard-grid">
      <!-- Left Side: Map -->
      <div class="map-area card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <h2>{{ $t('home.cameraDistribution') }} <span style="color: #007bff; font-size: 0.8em;">({{ cameras.length }} 个摄像头)</span></h2>
          <button 
            @click="refreshMapData" 
            style="background-color: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-size: 14px;"
            title="刷新摄像头数据">
            🔄 刷新
          </button>
        </div>
        <div id="map-container">
          <div id="google-map"></div>
          <!-- 删除了静态摄像头标记，只保留Leaflet动态标记 -->
        </div>
      </div>

      <!-- Right Side: Alarms -->
      <div class="alarms-area card">
        <h2>{{ $t('home.latestAlarms') }}</h2>
        <div class="alarm-list">
          <ul>
            <li v-for="(alarm, index) in alarms" :key="index" class="alarm-item" @click="showAlarmDetails(alarm)">
              <div class="event">{{ alarm.event }}</div>
              <div class="location">{{ alarm.location }}</div>
              <div class="time">{{ alarm.time }}</div>
            </li>
          </ul>
        </div>
        <div class="chart-area">
          <h2>{{ $t('home.alarmCount') }}</h2>
          <div class="chart-container">
            <canvas id="today-alarms-chart"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal for Alarm Video Popup -->
    <div id="alarm-modal" class="modal" v-if="isModalVisible" @click.self="closeModal">
      <div class="modal-content">
        <span class="close-btn" @click="closeModal">&times;</span>
        <h2 id="modal-title">{{ selectedAlarm?.title }}</h2>
        <img id="modal-image" :src="selectedAlarm?.image" alt="报警实时画面">
        <p id="modal-description">{{ selectedAlarm?.description }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, onActivated, onUnmounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import Chart from 'chart.js/auto';
import { useCameraStore } from '@/stores/cameraStore';
import { storeToRefs } from 'pinia';

import { getAlarms, getTodayAlarmStats } from '@/services/alarmService';

const router = useRouter();
const route = useRoute();
const { t: $t } = useI18n();
const cameraStore = useCameraStore();
const { cameras, sites } = storeToRefs(cameraStore);
const { getSiteName } = cameraStore;

// --- Reactive data ---
const alarms = ref<any[]>([]);

const isModalVisible = ref(false);
const selectedAlarm = ref<{ title: string; image: string; description: string } | null>(null);
let map: L.Map | null = null;
let isSettingUpMap = false; // 防止重复调用的标志
let alarmsChart: Chart | null = null; // 保存图表实例

// --- Modal methods ---
function showAlarmDetails(alarm: { event: string; location: string; time: string; }) {
  selectedAlarm.value = {
    title: `报警详情: ${alarm.event}`,
    image: 'https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=800',
    description: `在 ${alarm.location} 于 ${alarm.time} 发生。`
  };
  isModalVisible.value = true;
}

function closeModal() {
  isModalVisible.value = false;
  selectedAlarm.value = null;
}

function goToCameraPoints(cameraId: number) {
  const cameraInfo = cameras.value.find(c => c.id === cameraId);
  if (cameraInfo) {
    const navigationData = {
      camera: cameraInfo,
      gridSize: 1,
      page: 1
    };
    sessionStorage.setItem('cameraNavigationData', JSON.stringify(navigationData));
    router.push('/camera-points');
  }
}

// 获取告警数据
async function fetchAlarms() {
  try {
    const alarmsData = await getAlarms({});
    // 转换为首页所需的格式
    alarms.value = alarmsData.map(alarm => ({
      event: alarm.type,
      location: alarm.location,
      time: alarm.time,
      cam: 1 // 默认摄像头ID，如果需要可以从告警数据中获取
    }));
  } catch (error) {
    console.error('获取告警数据失败:', error);
    alarms.value = []; // 如果获取失败，显示空列表
  }
}

// 更新图表数据
async function updateChart() {
  console.log('[HomeView] 📊 开始更新图表数据...');
  try {
    const stats = await getTodayAlarmStats();
    if (stats && stats.labels.length > 0 && alarmsChart) {
      // 使用 i18n 翻译从后端获取的标签
      const translatedLabels = stats.labels.map(label => {
        // 创建一个映射，将后端的中文标签映射到 i18n 的键
        const keyMap: { [key: string]: string } = {
          "人员禁区": 'home.alarmTypes.personForbidden',
          "车辆禁区": 'home.alarmTypes.vehicleForbidden',
          "人员入侵": 'home.alarmTypes.personIntrusion',
          "车辆入侵": 'home.alarmTypes.vehicleIntrusion'
        };
        const i18nKey = keyMap[label] || label; // 如果找不到映射，则使用原始标签
        return $t(i18nKey);
      });

      alarmsChart.data.labels = translatedLabels;
      alarmsChart.data.datasets[0].data = stats.data;
      
      // 示例：这里可以更新第二个数据集（例如，四日总量），如果后端提供了相应数据
      // alarmsChart.data.datasets[1].data = ... 
      
      alarmsChart.update();
      console.log('[HomeView] ✅ 图表更新成功:', stats);
    } else {
      console.warn('[HomeView] 获取的统计数据为空或图表未初始化');
    }
  } catch (error) {
    console.error('[HomeView] ❌ 更新图表数据失败:', error);
  }
}


// 手动刷新数据
async function refreshMapData() {
  console.log('[HomeView] Manual refresh triggered');
  try {
    // 强制刷新摄像头数据
    await cameraStore.fetchAllData(true); // 传入 true 强制刷新
    console.log('[HomeView] Manual refresh completed, cameras count:', cameras.value.length);
  } catch (error) {
    console.error('[HomeView] Manual refresh failed:', error);
  }
}

async function setupMap() {
  // 防止重复调用 - 更严格的检查
  if (isSettingUpMap) {
    console.log('[HomeView] setupMap 已在进行中，跳过重复调用');
    return;
  }
  
  isSettingUpMap = true;
  console.log('[HomeView] 开始 setupMap...');
  
  try {
    const currentCameraCount = cameras.value.length;
    console.log(`[HomeView] setupMap called - 当前摄像头数量: ${currentCameraCount}`);
    console.log(`[HomeView] 完整摄像头数据:`, JSON.stringify(cameras.value, null, 2));
    
    // 详细显示每个摄像头的坐标信息
    cameras.value.forEach((camera, index) => {
      console.log(`[HomeView] Camera ${index + 1} (ID: ${camera.id}): ${camera.name}`);
      console.log(`  - 坐标: [${camera.latitude}, ${camera.longitude}]`);
      console.log(`  - 坐标类型: lat=${typeof camera.latitude} (${camera.latitude}), lng=${typeof camera.longitude} (${camera.longitude})`);
      console.log(`  - 状态: ${camera.status}`);
      console.log(`  - 工地ID: ${camera.site_id}`);
    });
    
    const mapContainer = document.getElementById('google-map');
    if (mapContainer) {
      // 先彻底清理已存在的地图实例
      if (map) {
        console.log('[HomeView] 清理已存在的地图实例...');
        try {
          map.remove(); // 完全移除地图实例
          map = null;
          console.log('[HomeView] 地图实例已清理');
        } catch (cleanupError) {
          console.warn('[HomeView] 清理地图实例失败:', cleanupError);
          map = null; // 强制重置
        }
      }
      
      // 清理DOM容器内容并移除所有Leaflet相关的class和属性
      mapContainer.innerHTML = '';
      mapContainer.className = ''; // 清除所有CSS类
      mapContainer.removeAttribute('style'); // 清除style属性
      
      // 等待更长时间确保DOM清理完成
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 初始化新的地图实例
      console.log('[HomeView] 初始化新的地图实例...');
      try {
        map = L.map(mapContainer).setView([22.2600, 114.1828], 10); // 降低初始缩放级别
        L.tileLayer('https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}', {
          attribution: '© Google Maps',
          minZoom: 8,
          maxZoom: 18,
        }).addTo(map);
        console.log('[HomeView] ✅ 新地图实例初始化成功');
      } catch (error) {
        console.error('[HomeView] 新地图初始化失败:', error);
        isSettingUpMap = false;
        return; // 如果地图初始化失败，直接返回
      }

      // 清除现有标记（每次都要清除）
      let existingMarkerCount = 0;
      map.eachLayer((layer) => {
        if (layer instanceof L.Marker) {
          map?.removeLayer(layer);
          existingMarkerCount++;
        }
      });
      console.log(`[HomeView] 清除了 ${existingMarkerCount} 个已存在的标记`);

      console.log(`[HomeView] 开始添加 ${currentCameraCount} 个摄像头标记到地图`);
      let markersAdded = 0;
      const bounds = L.latLngBounds([]); // 创建边界对象用于自动调整地图视图
    
      cameras.value.forEach((camera, cameraIndex) => {
        console.log(`[HomeView] 处理摄像头 ${cameraIndex + 1}/${currentCameraCount} - ID: ${camera.id}: lat=${camera.latitude}, lng=${camera.longitude}`);
        console.log(`[HomeView] 坐标检查 - lat存在: ${!!camera.latitude}, lng存在: ${!!camera.longitude}`);
        console.log(`[HomeView] 坐标值检查 - lat值: ${camera.latitude} (${typeof camera.latitude}), lng值: ${camera.longitude} (${typeof camera.longitude})`);
        
        // 更严格的坐标验证
        const hasValidLat = camera.latitude !== null && camera.latitude !== undefined && !isNaN(Number(camera.latitude)) && Number(camera.latitude) !== 0;
        const hasValidLng = camera.longitude !== null && camera.longitude !== undefined && !isNaN(Number(camera.longitude)) && Number(camera.longitude) !== 0;
        
        console.log(`[HomeView] 有效坐标检查 - lat有效: ${hasValidLat}, lng有效: ${hasValidLng}`);
        
        if (hasValidLat && hasValidLng) {
          const statusColor = camera.status === '在线' ? '#007bff' : '#dc3545';
          // 使用摄像头图标而不是emoji
          const iconHtml = `<div style="background-color: ${statusColor}; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);">📹</div>`;
          const customIcon = L.divIcon({
            html: iconHtml,
            className: 'custom-div-icon',
            iconSize: [30, 30],
            iconAnchor: [15, 15]
          });

          const lat = Number(camera.latitude);
          const lng = Number(camera.longitude);
          const latLng = L.latLng(lat, lng);
          console.log(`[HomeView] 创建标记坐标: [${lat}, ${lng}]`);
          
          const marker = L.marker(latLng, { icon: customIcon }).addTo(map!);
          bounds.extend(latLng); // 将摄像头坐标添加到边界中
          markersAdded++;
          console.log(`[HomeView] ✓ 成功添加摄像头 ${camera.id} (${camera.name}) 的标记到 [${lat}, ${lng}]`);
          console.log(`[HomeView] 当前已添加标记数量: ${markersAdded}`);
          
          marker.on('click', () => goToCameraPoints(camera.id));

          // 创建安全的tooltip内容，避免在字符串模板中使用$t函数
          const siteName = getSiteName(camera.site_id);
          const cameraLocation = camera.location || '未知位置';
          const tooltipContent = `
            <div style="text-align: left; min-width: 200px;">
              <h4 style="margin: 0 0 8px 0; color: #333;">${camera.name}</h4>
              <div style="font-size: 12px; line-height: 1.4;">
                <p style="margin: 2px 0;"><strong>ID:</strong> ${camera.id}</p>
                <p style="margin: 2px 0;"><strong>状态:</strong> <span style="color: ${camera.status === '在线' ? '#28a745' : '#dc3545'}; font-weight: bold;">${camera.status}</span></p>
                <p style="margin: 2px 0;"><strong>所属工地:</strong> ${siteName}</p>
                <p style="margin: 2px 0;"><strong>位置:</strong> ${cameraLocation}</p>
                <p style="margin: 2px 0;"><strong>坐标:</strong> ${camera.latitude.toFixed(4)}, ${camera.longitude.toFixed(4)}</p>
                <p style="margin: 8px 0 0 0; font-size: 11px; color: #666;">点击查看详情</p>
              </div>
            </div>
          `;
          marker.bindTooltip(tooltipContent, { direction: 'top', offset: [0, -10], className: 'custom-tooltip', permanent: false, sticky: true });
        } else {
          console.warn(`[HomeView] ⚠️ 摄像头 ${camera.id} (${camera.name}) 坐标无效:`);
          console.warn(`  - latitude: ${camera.latitude} (类型: ${typeof camera.latitude})`);
          console.warn(`  - longitude: ${camera.longitude} (类型: ${typeof camera.longitude})`);
          console.warn(`  - hasValidLat: ${hasValidLat}, hasValidLng: ${hasValidLng}`);
        }
      });
      
      // 自动调整地图视图以包含所有摄像头
      console.log(`[HomeView] 边界检查: bounds.isValid()=${bounds.isValid()}, markersAdded=${markersAdded}`);
      if (markersAdded > 0 && bounds.isValid()) {
        console.log(`[HomeView] 自动调整地图视图以包含所有 ${markersAdded} 个摄像头`);
        console.log(`[HomeView] 边界范围:`, bounds.toBBoxString());
        map.fitBounds(bounds, { padding: [20, 20] }); // 调整地图以显示所有摄像头
        console.log(`[HomeView] 地图视图已调整`);
      } else {
        console.warn(`[HomeView] 无法调整地图视图: markersAdded=${markersAdded}, bounds.isValid()=${bounds.isValid()}`);
      }
      
      console.log(`[HomeView] ✅ 地图设置完成！成功添加 ${markersAdded} 个摄像头标记（共 ${currentCameraCount} 个摄像头）`);
      
      // 额外检查: 计算地图上实际的marker数量
      let actualMarkerCount = 0;
      map.eachLayer((layer) => {
        if (layer instanceof L.Marker) {
          actualMarkerCount++;
        }
      });
      console.log(`[HomeView] 地图上实际marker数量验证: ${actualMarkerCount}`);
    } // 关闭 mapContainer if 语句块
    
    console.log('[HomeView] setupMap 完成');
  } catch (error) {
    console.error('[HomeView] setupMap 错误:', error);
  } finally {
    isSettingUpMap = false; // 重置标志
  }
}

// 监听路由变化，当用户导航到首页时强制刷新数据
watch(
  () => route.name,
  async (newRouteName, oldRouteName) => {
    console.log(`[HomeView] Route changed: ${oldRouteName} -> ${newRouteName}`);
    if (newRouteName === 'dashboard') {
      console.log('[HomeView] Navigated to dashboard, forcing data refresh...');
      try {
        // 强制刷新数据，确保从摄像头管理页面返回后数据是最新的
        await cameraStore.fetchAllData(true);
        console.log('[HomeView] Route navigation refresh completed, cameras count:', cameras.value.length);
        
        // 数据刷新后重新设置地图
        setupMap().catch(error => console.error('[HomeView] setupMap in route watch failed:', error));
      } catch (error) {
        console.error('[HomeView] Error during route navigation refresh:', error);
      }
    }
  }
);

// Watch for changes in cameras array (监听摄像头数组变化)
watch(
  () => cameras.value,
  (newCameras, oldCameras) => {
    const newCount = newCameras.length;
    const oldCount = oldCameras ? oldCameras.length : 0;
    
    console.log(`[HomeView] 摄像头数组变化: ${oldCount} -> ${newCount}`);
    console.log('[HomeView] 新摄像头数据:', newCameras.map(c => ({ id: c.id, name: c.name, lat: c.latitude, lng: c.longitude })));
    
    // 只要数据发生变化，就重新设置地图（去掉JSON比较，因为它可能不精确）
    if (newCount !== oldCount) {
      console.log('[HomeView] 检测到摄像头数量变化，重新设置地图');
      setupMap().catch(error => console.error('[HomeView] setupMap in watch failed:', error));
    } else if (oldCount > 0) {
      // 数量相同但可能内容发生了变化，也重新设置地图
      console.log('[HomeView] 摄像头数量相同但内容可能变化，重新设置地图');
      setupMap().catch(error => console.error('[HomeView] setupMap in watch (same count) failed:', error));
    }
  },
  { deep: true, immediate: false }
);

// --- Lifecycle hook ---
onMounted(async () => {
  try {
    // 检查AppLayout是否已经获取了数据
    const hasData = cameras.value.length > 0 || sites.value.length > 0;
    
    // 只有在没有数据时才获取（避免与AppLayout重复获取）
    if (!hasData) {
      await cameraStore.fetchAllData();
    }
    
    await fetchAlarms();
    setupMap().catch(error => console.error('[HomeView] setupMap in onMounted failed:', error));
  } catch (error) {
    console.error('[HomeView] Error during mounting:', error);
  }

  // --- Initialize Chart ---
  const chartCanvas = document.getElementById('today-alarms-chart') as HTMLCanvasElement;
  if (chartCanvas) {
    const existingChart = Chart.getChart(chartCanvas);
    if (existingChart) {
      existingChart.destroy();
    }
    
    const ctx = chartCanvas.getContext('2d');
    if (ctx) {
      alarmsChart = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: [], // 初始为空，将由 updateChart 填充
          datasets: [
            { 
              label: $t('home.chartLabels.todayAlarms'), 
              data: [], // 初始为空
              backgroundColor: 'rgba(0, 123, 255, 0.5)', 
              borderColor: 'rgba(0, 123, 255, 1)', 
              borderWidth: 1 
            }, 
            { 
              label: $t('home.chartLabels.fourDayTotal'), 
              data: [], // 示例数据，可以根据需要从后端获取
              type: 'line', 
              fill: false, 
              borderColor: 'rgba(217, 83, 79, 1)', 
              tension: 0.1 
            }
          ]
        },
        options: { 
          responsive: true, 
          maintainAspectRatio: false, 
          scales: { y: { beginAtZero: true } }, 
          plugins: { 
            tooltip: { mode: 'index', intersect: false }, 
            legend: { position: 'top' } 
          } 
        }
      });
      
      // 获取真实数据并更新图表
      await updateChart();
    }
  }
});

// 当组件被激活时（从其他页面返回时）强制刷新数据
onActivated(async () => {
  console.log('[HomeView] Component activated, forcing data refresh...');
  try {
    // 强制刷新摄像头数据，确保从其他页面返回时数据是最新的
    await cameraStore.fetchAllData(true); // 传入 true 强制刷新
    await fetchAlarms(); // 重新获取告警列表
    await updateChart(); // 重新获取图表数据
    
    console.log('[HomeView] Activation refresh completed, cameras count:', cameras.value.length);
    
    // 数据刷新后重新设置地图
    setupMap().catch(error => console.error('[HomeView] setupMap in onActivated failed:', error));
  } catch (error) {
    console.error('[HomeView] Error during activation refresh:', error);
  }
});

// 组件卸载时清理地图和图表资源
onUnmounted(() => {
  if (map) {
    map.remove();
    map = null;
  }
  if (alarmsChart) {
    alarmsChart.destroy();
    alarmsChart = null;
  }
  isSettingUpMap = false;
});
</script>

<style scoped>
/* Scoped styles are not strictly necessary as global styles are applied, */
/* but can be used for component-specific adjustments. */
#google-map {
  z-index: 1; /* Ensure map is clickable */
}
</style>

<style>
/* 全局样式，用于自定义Leaflet工具提示 */
.custom-tooltip {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid #ddd !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 12px !important;
  line-height: 1.4 !important;
  color: #333 !important;
  backdrop-filter: blur(10px) !important;
}

.custom-tooltip::before {
  border-top-color: #ddd !important;
}

.custom-tooltip::after {
  border-top-color: rgba(255, 255, 255, 0.95) !important;
}

/* 悬停提示中的状态颜色 */
.custom-tooltip .status-online {
  color: #28a745 !important;
  font-weight: bold !important;
}

.custom-tooltip .status-offline {
  color: #dc3545 !important;
  font-weight: bold !important;
}

/* 地图标记悬停效果 */
.custom-div-icon:hover {
  transform: scale(1.1);
  transition: transform 0.2s ease;
  z-index: 1000;
}
</style>
