<template>
  <div class="container">
    <div class="header">
      <h1>{{ $t('alarms.title') }}</h1>
      <div class="header-controls">
        <div class="search-box">
          <input
            type="text"
            v-model="searchTerm"
            :placeholder="$t('alarms.filters.search')"
            class="search-input"
          />
          <button class="search-btn">🔍</button>
        </div>
        <div class="filter-controls">
          <select v-model="statusFilter" class="filter-select">
            <option value="">{{ $t('alarms.filters.status') }}</option>
            <option value="未确认">{{ $t('alarms.status.unconfirmed') }}</option>
            <option value="已确认">{{ $t('alarms.status.confirmed') }}</option>
            <option value="误报">{{ $t('alarms.status.falseAlarm') }}</option>
          </select>
          <select v-model="typeFilter" class="filter-select">
            <option value="">{{ $t('alarms.filters.type') }}</option>
            <option value="施工人员禁区作业">施工人员禁区作业</option>
            <option value="工程车辆禁区作业">工程车辆禁区作业</option>
            <option value="施工人员违规闯入">施工人员违规闯入</option>
            <option value="工程车辆违规闯入">工程车辆违规闯入</option>
          </select>
        </div>
      </div>
    </div>

    <div class="table-container">
      <table class="alarms-table">
        <thead>
          <tr>
            <th>{{ $t('alarms.table.id') }}</th>
            <th>{{ $t('alarms.table.time') }}</th>
            <th>{{ $t('alarms.table.type') }}</th>
            <th>{{ $t('alarms.table.location') }}</th>
            <th>{{ $t('alarms.table.screenshot') }}</th>
            <th>{{ $t('alarms.table.status') }}</th>
            <th>{{ $t('alarms.table.handler') }}</th>
            <th>{{ $t('alarms.table.actions') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="alarm in filteredAlarms" :key="alarm.id" class="alarm-row" @click="showAlarmDetail(alarm)">
            <td>{{ alarm.id }}</td>
            <td>{{ alarm.time }}</td>
            <td>{{ alarm.type }}</td>
            <td>{{ alarm.location }}</td>
            <td>
              <img
                :src="alarm.screenshot"
                :alt="alarm.type"
                class="screenshot-thumbnail"
                @click="showImage(alarm.screenshot)"
              />
            </td>
            <td>
              <span :class="getStatusClass(alarm.status)">{{ alarm.status }}</span>
            </td>
            <td>{{ alarm.handler }}</td>
            <td>
              <div class="action-buttons">
                <button
                  @click="editAlarm(alarm)"
                  class="btn-edit"
                >
                  {{ $t('alarms.actions.edit') }}
                </button>
                <button
                  @click="deleteAlarm(alarm.id)"
                  class="btn-delete"
                >
                  {{ $t('alarms.actions.delete') }}
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 图片预览模态框 -->
    <div v-if="showImageModal" class="image-modal" @click="closeImageModal">
      <div class="modal-content">
        <img :src="selectedImage" alt="报警截图" class="modal-image" />
        <button class="close-btn" @click="closeImageModal">×</button>
      </div>
    </div>

    <!-- 详情模态框 -->
    <div v-if="showDetailModal" class="detail-modal">
      <div class="modal-content">
        <span class="close-btn" @click="closeDetailModal">×</span>
        <h3>报警详情</h3>
        <div v-if="selectedAlarm" class="alarm-detail">
          <div class="detail-image">
            <img :src="selectedAlarm.screenshot" :alt="selectedAlarm.type" class="detail-screenshot" />
          </div>
          <div class="detail-info">
            <div class="detail-row">
              <label>报警ID:</label>
              <span>{{ selectedAlarm.id }}</span>
            </div>
            <div class="detail-row">
              <label>报警时间:</label>
              <span>{{ selectedAlarm.time }}</span>
            </div>
            <div class="detail-row">
              <label>报警类型:</label>
              <span>{{ selectedAlarm.type }}</span>
            </div>
            <div class="detail-row">
              <label>摄像头位置:</label>
              <span>{{ selectedAlarm.location }}</span>
            </div>
            <div class="detail-row">
              <label>当前状态:</label>
              <span :class="getStatusClass(selectedAlarm.status)">{{ selectedAlarm.status }}</span>
            </div>
            <div class="detail-row">
              <label>处理人:</label>
              <span>{{ selectedAlarm.handler }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑模态框 -->
    <div v-if="showEditModal" class="edit-modal">
      <div class="modal-content">
        <h3>编辑报警记录</h3>
        <form @submit.prevent="saveAlarm">
          <div class="form-group">
            <label>时间:</label>
            <input v-model="editForm.time" type="text" required />
          </div>
          <div class="form-group">
            <label>类型:</label>
            <input v-model="editForm.type" type="text" required />
          </div>
          <div class="form-group">
            <label>位置:</label>
            <input v-model="editForm.location" type="text" required />
          </div>
          <div class="form-group">
            <label>状态:</label>
            <select v-model="editForm.status">
              <option value="未确认">未确认</option>
              <option value="已确认">已确认</option>
              <option value="误报">误报</option>
            </select>
          </div>
          <div class="form-group">
            <label>处理人:</label>
            <input v-model="editForm.handler" type="text" />
          </div>
          <div class="form-actions">
            <button type="submit" class="btn-save">保存</button>
            <button type="button" @click="closeEditModal" class="btn-cancel">取消</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { t: $t } = useI18n()

// 定义报警记录类型
interface Alarm {
  id: number
  time: string
  type: string
  location: string
  screenshot: string
  status: string
  handler: string
}

// 响应式数据
const alarms = ref<Alarm[]>([])
const searchTerm = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const showImageModal = ref(false)
const selectedImage = ref('')
const showDetailModal = ref(false)
const selectedAlarm = ref<Alarm | null>(null)
const showEditModal = ref(false)
const editForm = ref<Partial<Alarm>>({})
const editingAlarmId = ref<number | null>(null)

// 计算属性：过滤后的报警记录
const filteredAlarms = computed(() => {
  return alarms.value.filter(alarm => {
    const matchesSearch = alarm.type.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                         alarm.location.toLowerCase().includes(searchTerm.value.toLowerCase())
    const matchesStatus = !statusFilter.value || alarm.status === statusFilter.value
    const matchesType = !typeFilter.value || alarm.type === typeFilter.value
    
    return matchesSearch && matchesStatus && matchesType
  })
})

// 获取报警记录数据
const fetchAlarms = async () => {
  try {
    const response = await fetch('/api/alarms/')
    
    if (response.ok) {
      const data = await response.json()
      alarms.value = data
      console.log('✅ 从数据库获取数据成功:', data)
    } else {
      console.error('❌ 获取报警记录失败:', response.statusText)
      alarms.value = [] // 清空数据，不显示任何内容
    }
  } catch (error) {
    console.error('❌ 获取报警记录出错:', error)
    alarms.value = [] // 清空数据，不显示任何内容
  }
}






// 确认报警
const confirmAlarm = async (id: number) => {
  try {
    const response = await fetch(`http://localhost:8000/api/alarms/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status: '已确认' })
    })
    
    if (response.ok) {
      await fetchAlarms() // 重新获取数据
    }
  } catch (error) {
    console.error('确认报警失败:', error)
  }
}

// 标记为误报
const markAsFalseAlarm = async (id: number) => {
  try {
    const response = await fetch(`http://localhost:8000/api/alarms/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status: '误报' })
    })
    
    if (response.ok) {
      await fetchAlarms() // 重新获取数据
    }
  } catch (error) {
    console.error('标记误报失败:', error)
  }
}

// 编辑报警记录
const editAlarm = (alarm: Alarm) => {
  editForm.value = { ...alarm }
  editingAlarmId.value = alarm.id
  showEditModal.value = true
}

// 保存报警记录
const saveAlarm = async () => {
  if (!editingAlarmId.value) return
  
  try {
    const response = await fetch(`http://localhost:8000/api/alarms/${editingAlarmId.value}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(editForm.value)
    })
    
    if (response.ok) {
      await fetchAlarms() // 重新获取数据
      closeEditModal()
    }
  } catch (error) {
    console.error('保存报警记录失败:', error)
  }
}

// 删除报警记录
const deleteAlarm = async (id: number) => {
  if (!confirm('确定要删除这条报警记录吗？')) return
  
  try {
    const response = await fetch(`http://localhost:8000/api/alarms/${id}`, {
      method: 'DELETE'
    })
    
    if (response.ok) {
      await fetchAlarms() // 重新获取数据
    }
  } catch (error) {
    console.error('删除报警记录失败:', error)
  }
}

// 显示图片
const showImage = (imageUrl: string) => {
  selectedImage.value = imageUrl
  showImageModal.value = true
}

// 关闭图片模态框
const closeImageModal = () => {
  showImageModal.value = false
  selectedImage.value = ''
}

// 显示报警详情
const showAlarmDetail = (alarm: Alarm) => {
  selectedAlarm.value = alarm
  showDetailModal.value = true
}

// 关闭详情模态框
const closeDetailModal = () => {
  showDetailModal.value = false
  selectedAlarm.value = null
}



// 关闭编辑模态框
const closeEditModal = () => {
  showEditModal.value = false
  editForm.value = {}
  editingAlarmId.value = null
 }

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case '未确认':
      return 'status-pending'
    case '已确认':
      return 'status-confirmed'
    case '误报':
      return 'status-false'
    default:
      return 'status-default'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchAlarms()
})
</script>

<style scoped>
.container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header h1 {
  margin: 0;
  color: #1e3a8a;
  font-size: 28px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  gap: 20px;
  align-items: center;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input {
  padding: 10px 15px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  width: 250px;
}

.search-input:focus {
  outline: none;
  border-color: #1e3a8a;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.search-btn {
  padding: 10px 15px;
  background: #1e3a8a;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
}

.search-btn:hover {
  background: #1e40af;
}

.filter-controls {
  display: flex;
  gap: 15px;
}

.filter-select {
  padding: 10px 15px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: #1e3a8a;
}

.table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.alarms-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.alarms-table th {
  background: #f8fafc;
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.alarms-table td {
  padding: 15px 12px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.alarm-row:hover {
  background: #f9fafb;
}

.screenshot-thumbnail {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  transition: transform 0.2s;
}

.screenshot-thumbnail:hover {
  transform: scale(1.1);
}

.status-pending {
  color: #d97706;
  font-weight: 500;
}

.status-confirmed {
  color: #059669;
  font-weight: 500;
}

.status-false {
  color: #dc2626;
  font-weight: 500;
}

.status-default {
  color: #6b7280;
}

.action-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-confirm {
  background: #059669;
  color: white;
}

.btn-confirm:hover {
  background: #047857;
}

.btn-false-alarm {
  background: #dc2626;
  color: white;
}

.btn-false-alarm:hover {
  background: #b91c1c;
}

.btn-edit {
  background: #1e3a8a;
  color: white;
}

.btn-edit:hover {
  background: #1e40af;
}

.btn-delete {
  background: #6b7280;
  color: white;
}

.btn-delete:hover {
  background: #4b5563;
}

/* 模态框样式 */
.image-modal,
.edit-modal,
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  max-width: 90%;
  max-height: 90%;
  position: relative;
}

.image-modal .modal-content {
  padding: 0;
  background: transparent;
}

.modal-image {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.close-btn {
  position: absolute;
  top: -15px;
  right: -15px;
  width: 30px;
  height: 30px;
  border: none;
  background: #dc2626;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #b91c1c;
}

/* 详情模态框样式 */
.detail-modal .modal-content {
  min-width: 600px;
  max-width: 800px;
}

.alarm-detail {
  display: flex;
  gap: 20px;
}

.detail-image {
  flex: 1;
  text-align: center;
}

.detail-screenshot {
  max-width: 100%;
  max-height: 400px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-info {
  flex: 1;
}

.detail-row {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.detail-row label {
  font-weight: 600;
  color: #374151;
  display: inline-block;
  width: 100px;
  margin-right: 10px;
}

.detail-row span {
  color: #6b7280;
}



/* 编辑表单样式 */
.edit-modal .modal-content {
  min-width: 400px;
}

.edit-modal h3 {
  margin: 0 0 20px 0;
  color: #1e3a8a;
  font-size: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #1e3a8a;
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn-save {
  padding: 10px 20px;
  background: #059669;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.btn-save:hover {
  background: #047857;
}

.btn-cancel {
  padding: 10px 20px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.btn-cancel:hover {
  background: #4b5563;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .header-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .filter-controls {
    flex-direction: column;
  }
  
  .alarms-table {
    font-size: 12px;
  }
  
  .alarms-table th,
  .alarms-table td {
    padding: 10px 8px;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .edit-modal .modal-content {
    min-width: 90%;
    margin: 20px;
  }
}
</style>

