# backend/ai_config_manager.py

import logging
import threading
from typing import Dict, Any

from config import settings

logger = logging.getLogger(__name__)

class AIConfigManager:
    """
    一个线程安全的单例，用于管理全局的、可动态更新的 AI 检测配置。
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # 防止重复初始化
        if hasattr(self, '_initialized') and self._initialized:
            return
        
        with self._lock:
            self.config: Dict[str, Any] = {}
            self.model_class_names: Dict[int, str] = {}
            self._initialized = False

    def load_initial_config(self, model_class_names: Dict[int, str]):
        """在服务器启动时加载初始配置。"""
        if self._initialized:
            return
            
        with self._lock:
            self.model_class_names = model_class_names
            logger.info(f"正在从模型加载类别: {list(model_class_names.values())}")

            # 从 settings.py 构建初始配置
            initial_config = {}
            for class_id, class_name in model_class_names.items():
                # 默认所有类别都启用
                initial_config[class_name] = {
                    "enabled": True,
                    "confidence": settings.YOLO_CONF_THRESHOLDS.get(class_id, settings.YOLO_DEFAULT_CONF)
                }
            
            self.config = initial_config
            self._initialized = True
            logger.info("✅ AI 配置管理器初始化完成。")
            logger.debug(f"初始 AI 配置: {self.config}")

    def get_all_config(self) -> Dict[str, Any]:
        """获取当前完整的 AI 配置。"""
        with self._lock:
            return self.config.copy()

    def update_config(self, new_config: Dict[str, Any]):
        """用前端发来的新配置更新当前配置。"""
        with self._lock:
            for class_name, params in new_config.items():
                if class_name in self.config:
                    if 'enabled' in params:
                        self.config[class_name]['enabled'] = bool(params['enabled'])
                    if 'confidence' in params:
                        self.config[class_name]['confidence'] = float(params['confidence'])
                else:
                    logger.warning(f"尝试更新一个不存在的类别 '{class_name}' 的配置。")
            logger.info("AI 配置已更新。")
            logger.debug(f"更新后的 AI 配置: {self.config}")

    def get_class_config(self, class_name: str) -> Dict[str, Any]:
        """获取单个类别的当前配置。"""
        with self._lock:
            # 返回一个副本以防止外部修改
            return self.config.get(class_name, {"enabled": False, "confidence": 1.0})

# 创建全局单例
ai_config_manager = AIConfigManager()
