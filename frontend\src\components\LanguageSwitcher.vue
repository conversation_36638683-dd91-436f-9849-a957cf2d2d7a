<template>
  <div class="language-switcher">
    <div class="current-language" @click="toggleDropdown">
      <span class="flag">{{ currentFlag }}</span>
      <span class="name">{{ currentLanguageName }}</span>
      <span class="arrow" :class="{ 'rotated': showDropdown }">▼</span>
    </div>
    
    <div v-if="showDropdown" class="dropdown">
      <div 
        v-for="lang in languages" 
        :key="lang.code"
        class="language-option"
        :class="{ 'active': lang.code === currentLocale }"
        @click="changeLanguage(lang.code)"
      >
        <span class="flag">{{ lang.flag }}</span>
        <span class="name">{{ lang.name }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

const { locale } = useI18n()

// 语言选项
const languages = [
  { code: 'zh', name: '简体中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'zh-TW', name: '繁體中文', flag: '🇹🇼' }
]

// 当前语言信息
const currentLocale = computed(() => locale.value)
const currentLanguageName = computed(() => {
  const lang = languages.find(l => l.code === currentLocale.value)
  return lang ? lang.name : '简体中文'
})
const currentFlag = computed(() => {
  const lang = languages.find(l => l.code === currentLocale.value)
  return lang ? lang.flag : '🇨🇳'
})

// 下拉菜单状态
const showDropdown = ref(false)

// 切换下拉菜单
const toggleDropdown = () => {
  showDropdown.value = !showDropdown.value
}

// 切换语言
const changeLanguage = (langCode: string) => {
  locale.value = langCode
  showDropdown.value = false
  
  // 保存到localStorage
  localStorage.setItem('preferred-language', langCode)
  
  // 切换语言后，i18n会自动更新所有使用了$t的文本，无需刷新页面
  // // 强制刷新页面以应用新语言
  // setTimeout(() => {
  //   window.location.reload()
  // }, 100)
}

// 初始化时从localStorage读取语言偏好
const initLanguage = () => {
  const savedLang = localStorage.getItem('preferred-language')
  if (savedLang && languages.some(l => l.code === savedLang)) {
    locale.value = savedLang
  } else {
    locale.value = 'zh'
  }
}

// 组件挂载时初始化
onMounted(() => {
  initLanguage()
})
</script>

<style scoped>
.language-switcher {
  position: relative;
  display: block;
  width: 100%;
}

.current-language {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease;
  color: #333;
}

.current-language:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.flag {
  font-size: 18px;
}

.name {
  font-size: 14px;
  font-weight: 500;
  flex: 1;
}

.arrow {
  font-size: 10px;
  color: #666;
  transition: transform 0.2s ease;
}

.arrow.rotated {
  transform: rotate(180deg);
}

.dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.language-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #333;
}

.language-option:hover {
  background: #f8f9fa;
}

.language-option.active {
  background: #e3f2fd;
  color: #1976d2;
}

.language-option .flag {
  font-size: 16px;
}

.language-option .name {
  font-size: 14px;
  font-weight: 500;
}
</style> 