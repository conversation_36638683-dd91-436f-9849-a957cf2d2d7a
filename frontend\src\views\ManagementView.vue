<template>
  <div class="container">
    <!-- Page Header -->
    <div class="page-header">
      <h1>{{ $t('management.pageTitle') }}</h1>
      <div class="page-actions">
        <button id="add-site-btn" class="btn-primary" @click="openSiteModal()">
          <span class="btn-icon">🏗️</span> 添加工地
        </button>
        <button id="add-camera-btn" class="btn-primary" @click="openCameraModal()">
          <span class="btn-icon">📹</span> 添加摄像头
        </button>
        <button id="batch-delete-btn" class="btn-secondary" :disabled="selectedCameraIds.length === 0" @click="confirmBatchDelete">
          <span class="btn-icon">🗑️</span> 批量删除
        </button>
      </div>
    </div>

    <!-- Stats Grid -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">🏗️</div>
        <div class="stat-content">
          <div class="stat-number" id="total-sites">{{ stats.totalSites }}</div>
          <div class="stat-label">工地总数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📹</div>
        <div class="stat-content">
          <div class="stat-number" id="total-cameras">{{ stats.totalCameras }}</div>
          <div class="stat-label">摄像头总数</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🟢</div>
        <div class="stat-content">
          <div class="stat-number" id="online-cameras">{{ stats.onlineCameras }}</div>
          <div class="stat-label">在线摄像头</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🔴</div>
        <div class="stat-content">
          <div class="stat-number" id="offline-cameras">{{ stats.offlineCameras }}</div>
          <div class="stat-label">离线摄像头</div>
        </div>
      </div>
    </div>

    <!-- Site Management -->
    <div class="card">
      <div class="card-header">
        <h2>{{ $t('management.siteManagement') }}</h2>
        <div class="card-actions">
          <input type="text" id="search-site" placeholder="搜索工地名称" class="search-input" v-model="siteSearchTerm">
        </div>
      </div>
      <div class="site-list" id="site-list">
        <div v-for="site in filteredSites" :key="site.id" class="site-item" :data-site-id="site.id">
          <div class="site-header">
            <h3 class="site-title">{{ site.name }}</h3>
            <div class="site-actions">
              <button class="btn-edit" @click="openSiteModal(site)">编辑</button>
              <button class="btn-delete" @click="confirmDelete('site', site)">删除</button>
            </div>
          </div>
          <div class="site-info">
            <p><strong>工地ID：</strong>{{ site.id }}</p>
            <p><strong>描述：</strong>{{ site.description }}</p>
          </div>
          <div class="site-cameras">
            <h4>摄像头列表</h4>
            <div class="camera-count">{{ cameras.filter(c => c.site_id === site.id).length }} 个摄像头</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Camera Management -->
    <div class="card">
      <div class="card-header">
        <h2>{{ $t('management.cameraManagement') }}</h2>
        <div class="card-actions">
          <select id="site-filter" class="filter-select" v-model="cameraSiteFilter">
            <option value="">所有工地</option>
            <option v-for="site in sites" :key="site.id" :value="site.id">{{ site.name }}</option>
          </select>
          <select id="status-filter" class="filter-select" v-model="cameraStatusFilter">
            <option value="">所有状态</option>
            <option value="在线">在线</option>
            <option value="离线">离线</option>
            <option value="维修中">维修中</option>
          </select>
          <input type="text" id="search-camera" placeholder="搜索摄像头名称或ID" class="search-input" v-model="cameraSearchTerm">
        </div>
      </div>
      <div class="camera-table-container">
        <table class="camera-table">
          <thead>
            <tr>
              <th><input type="checkbox" id="select-all-cameras" @change="toggleSelectAll"></th>
              <th>摄像头ID</th>
              <th>名称</th>
              <th>所属工地</th>
              <th>摄像头位置</th>
              <th>状态</th>
              <th>IP地址</th>
              <th>经纬度</th>
              <th>最后在线时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody id="camera-table-body">
            <tr v-for="camera in filteredCameras" :key="camera.id">
              <td><input type="checkbox" class="camera-checkbox" v-model="selectedCameraIds" :value="camera.id"></td>
              <td>{{ camera.id }}</td>
              <td>{{ camera.name }}</td>
              <td>{{ getSiteName(camera.site_id) }}</td>
              <td>{{ camera.location || '-' }}</td>
              <td><span class="status-badge" :class="getStatusClass(camera.status)">{{ camera.status }}</span></td>
              <td>{{ camera.ip_address }}</td>
              <td>{{ camera.latitude.toFixed(4) }}, {{ camera.longitude.toFixed(4) }}</td>
              <td>{{ camera.last_online || '-' }}</td>
              <td>
                <button class="btn-edit" @click="openCameraModal(camera)">编辑</button>
                <button class="btn-delete" @click="confirmDelete('camera', camera)">删除</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Modals -->
    <div id="site-modal" class="modal" v-if="isSiteModalVisible" @click.self="closeSiteModal">
      <div class="modal-content">
        <div class="modal-header">
          <h2 id="site-modal-title">{{ siteForm.id ? '编辑工地' : '添加工地' }}</h2>
          <span class="close-btn" @click="closeSiteModal">&times;</span>
        </div>
        <form id="site-form" class="site-form" @submit.prevent="saveSite">
          <div class="form-group" v-if="siteForm.id"><label for="site-id">工地ID：</label><input type="number" id="site-id" v-model="siteForm.id" disabled></div>
          <div class="form-group"><label for="site-name">工地名称：<span style="color:red">*</span></label><input type="text" id="site-name" v-model="siteForm.name" required></div>
          <div class="form-group"><label for="site-area">地理区域：</label><input type="text" id="site-area" v-model="siteForm.area"></div>
          <div class="form-group"><label for="site-description">工地描述：</label><textarea id="site-description" v-model="siteForm.description" rows="3"></textarea></div>
          <div class="form-actions"><button type="submit" class="btn-primary">保存</button><button type="button" class="btn-secondary" @click="closeSiteModal">取消</button></div>
        </form>
      </div>
    </div>

    <div id="camera-modal" class="modal" v-if="isCameraModalVisible" @click.self="closeCameraModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="camera-modal-title">{{ cameraForm.id ? '编辑摄像头' : '添加摄像头' }}</h2>
                <span class="close-btn" @click="closeCameraModal">&times;</span>
            </div>
            <form id="camera-form" class="camera-form" @submit.prevent="saveCamera">
                <div class="form-row">
                    <div class="form-group" v-if="cameraForm.id"><label for="camera-id">摄像头ID：</label><input type="number" id="camera-id" v-model="cameraForm.id" disabled></div>
                    <div class="form-group"><label for="camera-name">摄像头名称：<span style="color:red">*</span></label><input type="text" id="camera-name" v-model="cameraForm.name" required></div>
                </div>
                <div class="form-row">
                    <div class="form-group"><label for="camera-site">所属工地：<span style="color:red">*</span></label>
                        <select id="camera-site" v-model="cameraForm.site_id" required>
                            <option value="">请选择工地</option>
                            <option v-for="site in sites" :key="site.id" :value="site.id">{{ site.name }}</option>
                        </select>
                    </div>
                    <div class="form-group"><label for="camera-ip">IP地址：<span style="color:red">*</span></label><input type="text" id="camera-ip" v-model="cameraForm.ip_address" required></div>
                    <div class="form-group"><label for="camera-port">端口：<span style="color:red">*</span></label><input type="number" id="camera-port" v-model="cameraForm.port" required></div>
                </div>
                <div class="form-row">
                    <div class="form-group"><label for="camera-username">用户名：</label><input type="text" id="camera-username" v-model="cameraForm.username"></div>
                    <div class="form-group"><label for="camera-password">密码：</label><input type="password" id="camera-password" v-model="cameraForm.password"></div>
                </div>
                <div class="form-group"><label for="camera-stream-path">流路径：</label><input type="text" id="camera-stream-path" v-model="cameraForm.stream_path" placeholder="例如: /Streaming/Channels/101"></div>
                <div class="form-group"><label for="camera-location">摄像头位置：</label><input type="text" id="camera-location" v-model="cameraForm.location"></div>
                <div class="form-row">
                    <div class="form-group"><label for="camera-latitude">纬度：<span style="color:red">*</span></label><input type="number" id="camera-latitude" v-model="cameraForm.latitude" step="0.000001" required></div>
                    <div class="form-group"><label for="camera-longitude">经度：<span style="color:red">*</span></label><input type="number" id="camera-longitude" v-model="cameraForm.longitude" step="0.000001" required></div>
                </div>
                <div class="form-actions"><button type="submit" class="btn-primary">保存</button><button type="button" class="btn-secondary" @click="closeCameraModal">取消</button></div>
            </form>
        </div>
    </div>

    <div id="delete-modal" class="modal" v-if="isDeleteModalVisible" @click.self="closeDeleteModal">
        <div class="modal-content">
            <div class="modal-header"><h2>确认删除</h2><span class="close-btn" @click="closeDeleteModal">&times;</span></div>
            <div class="modal-body">
                <div id="delete-message" class="delete-message">{{ deleteMessage }}</div>
                <p class="delete-warning">⚠️ 此操作不可撤销，请谨慎操作！</p>
            </div>
            <div class="modal-actions">
                <button id="confirm-delete-btn" class="btn-danger" @click="executeDelete">确认删除</button>
                <button class="btn-secondary" @click="closeDeleteModal">取消</button>
            </div>
        </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useCameraStore, type CameraCreatePayload } from '@/stores/cameraStore';
import type { Camera, Site, SiteCreate } from '@/services/cameraService';
import { storeToRefs } from 'pinia';

const { t: $t } = useI18n();
const cameraStore = useCameraStore();
const { sites, cameras } = storeToRefs(cameraStore);

// --- Data ---
const selectedCameraIds = ref<number[]>([]);

// Filters
const siteSearchTerm = ref('');
const cameraSearchTerm = ref('');
const cameraSiteFilter = ref<number | ''>('');
const cameraStatusFilter = ref<Camera['status'] | ''>('');

// Modals
const isSiteModalVisible = ref(false);
const isCameraModalVisible = ref(false);
const isDeleteModalVisible = ref(false);

const siteForm = ref<Partial<Site>>({ id: undefined, name: '', area: '', description: '' });
const cameraForm = ref<Partial<Camera>>({ id: undefined, name: '', site_id: undefined, location: '', ip_address: '', port: 554, username: '', password: '', stream_path: '', latitude: 22.2600, longitude: 114.1828 });

const deleteConfig = ref<{ type: string; item: Site | Camera | null }>({ type: '', item: null });
const deleteMessage = ref('');

// --- Computed Properties ---
const stats = computed(() => cameraStore.stats);

const filteredSites = computed(() => sites.value.filter(s => 
    s.name.toLowerCase().includes(siteSearchTerm.value.toLowerCase())
));

const filteredCameras = computed(() => cameras.value.filter(c => 
    (c.name.toLowerCase().includes(cameraSearchTerm.value.toLowerCase()) || c.id.toString().includes(cameraSearchTerm.value)) &&
    (cameraSiteFilter.value === '' || c.site_id === cameraSiteFilter.value) &&
    (cameraStatusFilter.value === '' || c.status === cameraStatusFilter.value)
));

// --- Methods ---
const getSiteName = (siteId: number) => cameraStore.getSiteName(siteId);
const getStatusClass = (status: Camera['status']) => ({ 'online': status === '在线', 'offline': status === '离线', 'maintenance': status === '维修中' });

function toggleSelectAll(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.checked) {
        selectedCameraIds.value = filteredCameras.value.map(c => c.id);
    } else {
        selectedCameraIds.value = [];
    }
}

// Site Modal
function openSiteModal(site: Site | null = null) {
    if (site) {
        siteForm.value = { ...site };
    } else {
        siteForm.value = { id: undefined, name: '', area: '', description: '' };
    }
    isSiteModalVisible.value = true;
}
function closeSiteModal() { isSiteModalVisible.value = false; }
async function saveSite() {
    if (siteForm.value.id) { // Editing
        await cameraStore.updateSite(siteForm.value.id, siteForm.value);
    } else { // Adding
        // 移除id字段以避免后端验证错误
        const { id, ...siteCreateData } = siteForm.value;
        await cameraStore.addSite(siteCreateData as SiteCreate);
    }
    closeSiteModal();
}

// Camera Modal
function openCameraModal(camera: Camera | null = null) {
    if (camera) {
        cameraForm.value = { ...camera };
    } else {
        cameraForm.value = { id: undefined, name: '', site_id: undefined, location: '', ip_address: '', port: 554, username: '', password: '', stream_path: '', latitude: 22.2600, longitude: 114.1828 };
    }
    isCameraModalVisible.value = true;
}
function closeCameraModal() { isCameraModalVisible.value = false; }
async function saveCamera() {
    if (cameraForm.value.id) { // Editing
        await cameraStore.updateCamera(cameraForm.value.id, cameraForm.value);
    } else { // Adding
        // 移除id字段和状态相关字段以避免后端验证错误
        const { id, status, last_online, ...cameraCreateData } = cameraForm.value;
        await cameraStore.addCamera(cameraCreateData as CameraCreatePayload);
    }
    closeCameraModal();
}

// Delete Logic
function confirmDelete(type: 'site' | 'camera', item: Site | Camera) {
    deleteConfig.value = { type, item };
    
    if (type === 'site') {
        // 统计该工地下的摄像头数量
        const siteCameras = cameras.value.filter(c => c.site_id === item.id);
        const cameraCount = siteCameras.length;
        
        if (cameraCount > 0) {
            deleteMessage.value = `确定要删除工地 "${item.name}" 吗？\n\n⚠️ 删除工地将同时删除该工地下的 ${cameraCount} 个摄像头及其所有预置点。`;
        } else {
            deleteMessage.value = `确定要删除工地 "${item.name}" 吗？`;
        }
    } else {
        deleteMessage.value = `确定要删除摄像头 "${item.name}" 吗？\n\n⚠️ 删除摄像头将同时删除该摄像头的所有预置点。`;
    }
    
    isDeleteModalVisible.value = true;
}
function confirmBatchDelete() {
    deleteConfig.value = { type: 'batchCamera', item: null };
    deleteMessage.value = `确定要删除选中的 ${selectedCameraIds.value.length} 个摄像头吗？`;
    isDeleteModalVisible.value = true;
}
function closeDeleteModal() { isDeleteModalVisible.value = false; }
async function executeDelete() {
    const { type, item } = deleteConfig.value;
    if (type === 'site' && item) {
        await cameraStore.deleteSite(item.id);
    } else if (type === 'camera' && item) {
        await cameraStore.deleteCamera(item.id);
    } else if (type === 'batchCamera') {
        await cameraStore.deleteMultipleCameras(selectedCameraIds.value);
        selectedCameraIds.value = [];
    }
    closeDeleteModal();
}

onMounted(() => {
  cameraStore.fetchAllData();
});
</script>

<style scoped>
/* Using global styles from main.css */

/* 删除弹窗样式 */
.delete-message {
  white-space: pre-line;
  line-height: 1.6;
  margin-bottom: 15px;
  color: #374151;
  font-size: 16px;
}

.delete-warning {
  margin-top: 15px;
  padding: 12px;
  background-color: #fef3cd;
  border: 1px solid #fdeaa7;
  border-radius: 6px;
  color: #664d03;
  font-weight: 500;
}
</style>