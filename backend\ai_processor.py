# backend/ai_processor.py

import asyncio
import cv2
import json
import logging
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Set

from ultralytics import <PERSON><PERSON><PERSON>

from config import settings
from ai_config_manager import ai_config_manager

logger = logging.getLogger(__name__)

class AIProcessor:
    def __init__(self):
        logger.info(f"正在加载 YOLO 模型: {settings.YOLO_MODEL_PATH}")
        try:
            self.model = YOLO(settings.YOLO_MODEL_PATH)
            ai_config_manager.load_initial_config(self.model.names)
            logger.info("✅ YOLO 模型加载成功并已初始化配置管理器。")
        except Exception as e:
            logger.error(f"❌ YOLO 模型加载失败: {e}", exc_info=True)
            raise

        self.active_analysis_tasks: Dict[int, threading.Thread] = {}
        self.output_dir = Path(settings.OUTPUT_DIR)
        self._stop_events: Dict[int, threading.Event] = {}
        self.live_viewers: Dict[int, Set[asyncio.Queue]] = {}
        self._lock = threading.Lock()

    def subscribe(self, camera_id: int) -> asyncio.Queue:
        with self._lock:
            queue = asyncio.Queue(maxsize=settings.VIDEO_FPS)
            if camera_id not in self.live_viewers:
                self.live_viewers[camera_id] = set()
            self.live_viewers[camera_id].add(queue)
            logger.info(f"新的观众已订阅摄像头 {camera_id}。当前观众数: {len(self.live_viewers[camera_id])}")
            return queue

    def unsubscribe(self, camera_id: int, queue: asyncio.Queue):
        with self._lock:
            if camera_id in self.live_viewers and queue in self.live_viewers[camera_id]:
                self.live_viewers[camera_id].remove(queue)
                if not self.live_viewers[camera_id]:
                    del self.live_viewers[camera_id]

    def start_analysis_task(self, camera_id: int, rtsp_url: str):
        with self._lock:
            if camera_id in self.active_analysis_tasks and self.active_analysis_tasks[camera_id].is_alive():
                return
            logger.info(f"为摄像头 {camera_id} 创建AI分析线程。")
            stop_event = threading.Event()
            # 获取当前在主线程中运行的事件循环
            loop = asyncio.get_running_loop()
            thread = threading.Thread(target=self._analysis_loop, args=(camera_id, rtsp_url, stop_event, loop))
            self._stop_events[camera_id] = stop_event
            self.active_analysis_tasks[camera_id] = thread
            thread.start()

    def stop_analysis_task(self, camera_id: int):
        with self._lock:
            if camera_id in self._stop_events:
                self._stop_events[camera_id].set()
            thread = self.active_analysis_tasks.pop(camera_id, None)
            if thread and thread.is_alive():
                thread.join(timeout=5)

    def stop_all_tasks(self):
        logger.info("正在停止所有AI分析任务...")
        with self._lock:
            for stop_event in self._stop_events.values():
                stop_event.set()
            for thread in self.active_analysis_tasks.values():
                if thread.is_alive():
                    thread.join(timeout=5)
        self.active_analysis_tasks.clear()
        self._stop_events.clear()
        logger.info("所有AI分析任务已停止。")

    def _analysis_loop(self, camera_id: int, rtsp_url: str, stop_event: threading.Event, loop: asyncio.AbstractEventLoop):
        retry_delay = 10
        while not stop_event.is_set():
            try:
                cap = cv2.VideoCapture(rtsp_url, cv2.CAP_FFMPEG)
                if not cap.isOpened(): raise ConnectionError("无法打开RTSP流")
                logger.info(f"✅ AI分析线程 {camera_id}: 连接成功。")
                
                while not stop_event.is_set():
                    ret, img = cap.read()
                    if not ret: break
                    
                    results = self.model.predict(img, verbose=False, stream=False)
                    annotated_frame = results[0].plot()

                    with self._lock:
                        if camera_id in self.live_viewers:
                            for queue in self.live_viewers[camera_id]:
                                try:
                                    loop.call_soon_threadsafe(queue.put_nowait, annotated_frame)
                                except asyncio.QueueFull:
                                    pass

            except Exception as e:
                logger.error(f"AI分析线程 {camera_id} 发生错误: {e}")
            finally:
                if 'cap' in locals() and cap.isOpened(): cap.release()

            if not stop_event.is_set():
                stop_event.wait(timeout=retry_delay)