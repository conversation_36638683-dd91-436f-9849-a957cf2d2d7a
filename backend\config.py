from pydantic_settings import BaseSettings
from pathlib import Path
from typing import Dict

# Build a path to the .env file relative to this config file.
env_path = Path(__file__).parent / ".env"

class Settings(BaseSettings):
    # --- Database ---
    DATABASE_URL: str

    # --- Edge Device (RK3568) ---
    RK3568_API_PORT: int = 8001

    # --- AI Processor ---
    YOLO_MODEL_PATH: str = str(Path(__file__).parent / 'model/best_new_new_new.pt')
    YOLO_BATCH_SIZE: int = 16
    YOLO_DEFAULT_CONF: float = 0.25
    YOLO_CONF_THRESHOLDS: Dict[int, float] = {
        0: 0.7,  # Person
        2: 0.55, # Car
        # Add other class IDs and their specific thresholds here
    }

    # --- Media Output ---
    OUTPUT_DIR: str = str(Path(__file__).parent.parent / "output")
    VIDEO_SEGMENT_DURATION: int = 300  # in seconds (5 minutes)
    VIDEO_FPS: float = 30.0 # Target FPS for output videos (matching typical camera FPS)
    VIDEO_BITRATE: str = "1500k" # Target bitrate for H.264
    JPEG_QUALITY: int = 80 # Compression quality for snapshots (0-100)

    class Config:
        env_file = env_path
        env_file_encoding = 'utf-8'

settings = Settings()
