// frontend/src/views/CameraPointsView.vue

<template>
  <!-- ... 模板部分保持不变 ... -->
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted, watch, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import Hls from 'hls.js';
import { useCameraStore } from '@/stores/cameraStore';
import { getLiveStreamUrl, type Camera } from '@/services/cameraService';

const { t: $t } = useI18n();
const cameraStore = useCameraStore();
const { allCameras } = storeToRefs(cameraStore);

// --- HLS.js & Video Logic ---
const videoElements = ref<Record<number, HTMLVideoElement | null>>({});
const activeHlsInstances = ref<Record<number, Hls | null>>({});
const streamStatus = ref<Record<number, { text: string; color: string }>>({});

async function startHlsStream(camera: Camera, videoEl: HTMLVideoElement | null) {
    if (!videoEl || activeHlsInstances.value[camera.id]) return;

    streamStatus.value[camera.id] = { text: '加载中...', color: 'connecting' };
    try {
        const response = await getLiveStreamUrl(camera.id);
        const hlsUrl = response.url;

        if (Hls.isSupported()) {
            const hls = new Hls();
            activeHlsInstances.value[camera.id] = hls;
            hls.loadSource(hlsUrl);
            hls.attachMedia(videoEl);
            hls.on(Hls.Events.MANIFEST_PARSED, () => { videoEl.play().catch(e => console.warn("播放被浏览器阻止")); streamStatus.value[camera.id] = { text: '在线', color: 'connected' }; });
            hls.on(Hls.Events.ERROR, (event, data) => {
                if (data.fatal) {
                    console.error(`HLS error for camera ${camera.id}:`, data);
                    streamStatus.value[camera.id] = { text: '错误', color: 'error' };
                }
            });
        } else if (videoEl.canPlayType('application/vnd.apple.mpegurl')) {
            videoEl.src = hlsUrl;
            videoEl.addEventListener('canplay', () => videoEl.play().catch(e => console.warn("播放被浏览器阻止")));
            streamStatus.value[camera.id] = { text: '在线', color: 'connected' };
        }
    } catch (error) {
        console.error(`获取摄像头 ${camera.id} 的HLS地址失败:`, error);
        streamStatus.value[camera.id] = { text: '加载失败', color: 'error' };
    }
}

function stopHlsStream(cameraId: number) {
    const hls = activeHlsInstances.value[cameraId];
    if (hls) {
        hls.destroy();
        delete activeHlsInstances.value[cameraId];
    }
    const videoEl = videoElements.value[cameraId];
    if(videoEl) { videoEl.src = ''; videoEl.load(); }
}

// --- UI & Data Logic ---
const currentGridSize = ref(16);
const currentPage = ref(1);

const totalPages = computed(() => Math.ceil(allCameras.value.length / currentGridSize.value));
const paginatedCameras = computed(() => {
  const startIndex = (currentPage.value - 1) * currentGridSize.value;
  const endIndex = startIndex + currentGridSize.value;
  return allCameras.value.slice(startIndex, endIndex);
});

// --- 核心逻辑：侦听分页后的摄像头列表变化 ---
watch(paginatedCameras, (newCameras, oldCameras) => {
    // 确保在DOM更新后再执行操作
    nextTick(() => {
        const newIds = new Set(newCameras.map(c => c.id));

        // 停止不再显示的旧视频流
        if (oldCameras) {
            oldCameras.forEach(cam => {
                if (!newIds.has(cam.id)) {
                    stopHlsStream(cam.id);
                }
            });
        }

        // 为新显示的摄像头加载视频流
        newCameras.forEach(cam => {
            startHlsStream(cam, videoElements.value[cam.id]);
        });
    });
}, { 
    immediate: true, // 关键：组件创建时立即执行一次，处理初始数据
    deep: true 
});

// --- Lifecycle Hooks ---
onUnmounted(() => {
  // 组件卸载时清理所有视频流
  allCameras.value.forEach(cam => stopHlsStream(cam.id));
});

// ... 其他UI逻辑 ...

</script>

<style scoped>
/* ... 样式保持不变 ... */
</style>
