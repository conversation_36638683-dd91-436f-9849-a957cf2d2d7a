16:01 2025/8/13<template>
  <div class="camera-points-layout-wrapper">
    <div class="camera-points-layout">
      <div class="tree-panel card">
        <h2>{{ $t('cameraPoints.cameraList') }}</h2>
        <div class="camera-stats">
          <span class="stats-item">
            <strong>工地数量:</strong> {{ siteStats.totalSites }}
          </span>
          <span class="stats-item">
            <strong>摄像头数量:</strong> {{ siteStats.totalCameras }}
          </span>
          <span class="stats-item">
            <strong>在线:</strong> {{ siteStats.onlineCameras }}
          </span>
        </div>
        <ul class="camera-tree">
          <li v-for="site in treeData" :key="site.id" class="tree-parent" :class="{ collapsed: !site.expanded }">
            <span class="tree-node site-node" @click="toggleArea(site)">
              <span class="site-icon">🏢</span>
              <span class="site-name">{{ site.name }}</span>
              <span class="camera-count">({{ site.cameras.length }})</span>
              <span class="online-indicator" v-if="getSiteOnlineCount(site) > 0">
                • {{ getSiteOnlineCount(site) }}在线
              </span>
            </span>
            <ul v-show="site.expanded">
              <li v-for="cam in site.cameras" :key="cam.id" class="tree-leaf" @click="selectCamera(cam)">
                <span class="tree-node camera-node" :class="{ active: activeCameraId === cam.id }">
                  <span class="camera-status-dot" :class="cam.status"></span>
                  <span class="camera-info">{{ cam.id }}号: {{ cam.name }}</span>
                  <span class="camera-location" v-if="cam.location">{{ cam.location }}</span>
                </span>
              </li>
            </ul>
          </li>
        </ul>
      </div>
      <div class="video-panel">
        <div class="card">
          <h2>{{ $t('cameraPoints.realTimeVideo') }}</h2>
          <div class="controls">
            <span>{{ $t('cameraPoints.gridMode') }}:</span>
            <button @click="setGridSize(1)" :class="{ active: currentGridSize === 1 }">1</button>
            <button @click="setGridSize(4)" :class="{ active: currentGridSize === 4 }">4</button>
            <button @click="setGridSize(9)" :class="{ active: currentGridSize === 9 }">9</button>
            <button @click="setGridSize(16)" :class="{ active: currentGridSize === 16 }">16</button>
          </div>
          <div class="video-grid" :class="`grid-${currentGridSize}`">
            <div 
              v-for="cam in paginatedCameras" 
              :key="cam.id" 
              class="video-item"
              :class="{ highlight: highlightedCameraId === cam.id }"
              :data-camera-id="cam.id" 
              @click="openDetailModal(cam)"
            >
              <video :ref="el => { if (el) videoElements[cam.id] = el as HTMLVideoElement }" autoplay playsinline muted class="video-player"></video>
              <div class="overlay">
                <span>{{ cam.id }}号: {{ cam.name }}</span>
                <span :class="statusColor">{{ statusText }}</span>
              </div>
            </div>
          </div>
          <div class="pagination-controls">
            <button @click="prevPage" :disabled="currentPage === 1" class="page-btn prev-btn">‹ {{ $t('cameraPoints.previousPage') }}</button>
            <span class="page-info">{{ $t('cameraPoints.pageInfo', { current: currentPage, total: totalPages }) }}</span>
            <button @click="nextPage" :disabled="currentPage === totalPages" class="page-btn next-btn">{{ $t('cameraPoints.nextPage') }} ›</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Camera Detail Modal -->
    <div class="modal" v-if="isModalVisible" @click.self="closeDetailModal">
        <div class="modal-content large">
            <span class="close-btn" @click="closeDetailModal">&times;</span>
            <div class="modal-header">
                <div class="title-with-restart">
                    <h2>{{ $t('cameraPoints.cameraTitle', { id: selectedCamera?.id, name: selectedCamera?.name }) }}</h2>
                    <button class="restart-btn" :title="$t('cameraPoints.restartCamera')">
                        <span class="btn-icon">🔄</span>
                        {{ $t('cameraPoints.restart') }}
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div class="left-panel">
                    <div class="video-wrapper">
                                                <video ref="modalVideoRef" autoplay playsinline muted class="video-player"></video>
                        <canvas ref="canvasRef" class="drawing-canvas"></canvas> 
                    </div>
                    <div class="video-control-buttons">
                        <button class="video-btn playback-btn" @click="viewPlayback(selectedCamera!)">
                            <span class="btn-icon">▶</span>
                            {{ $t('cameraPoints.viewPlayback') }}
                        </button>
                    </div>
                </div>
                <div class="config-detail-container">
                    <CameraControlPanel @direction="handleDirectionEvent" />
                    <div class="preset-controls">
                        <button class="preset-btn" @click="addPreset">
                            <span class="btn-icon">📍</span>
                            {{ $t('cameraPoints.addPreset') }}
                        </button>
                        <button class="preset-btn delete-all-btn" @click="deleteAllPresets" v-if="presets.length > 0">
                            <span class="btn-icon">🗑️</span>
                            {{ $t('cameraPoints.deleteAllPresets') }}
                        </button>
                    </div>

                    <!-- Preset List -->
                    <div class="preset-list" v-if="presets.length > 0">
                        <h4>{{ $t('cameraPoints.savedPresets') }}:</h4>
                        <ul>
                            <li v-for="p in presets" :key="p.id">
                                <span>{{ p.name }}</span>
                                <div class="preset-actions">
                                    <button @click="applyPreset(p)" class="apply-btn">{{ $t('cameraPoints.apply') }}</button>
                                    <button @click="deletePreset(p.id)" class="delete-btn">{{ $t('cameraPoints.delete') }}</button>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <h3>{{ $t('cameraPoints.alarmRulesConfig') }}</h3>
                    <div class="drawing-status" v-if="isDrawingMode">
                        <div class="status-indicator">
                            <span class="status-dot drawing"></span>
                            <span class="status-text">{{ $t('cameraPoints.drawingModeEnabled') }}</span>
                        </div>
                        <div class="drawing-info" v-if="currentPoints.length > 0">
                            <span>{{ $t('cameraPoints.pointsDrawn', { count: currentPoints.length }) }}</span>
                            <span v-if="currentPoints.length < 3" class="warning">{{ $t('cameraPoints.minimumPointsRequired') }}</span>
                        </div>
                    </div>
                    <form class="config-form">
                        <ul>
                            <li><label><input type="checkbox" v-model="alarmRuleForm.person"> {{ $t('cameraPoints.personDetection') }}</label></li>
                            <li><label><input type="checkbox" v-model="alarmRuleForm.helmet"> {{ $t('cameraPoints.helmetDetection') }}</label></li>
                            <li><label><input type="checkbox" v-model="alarmRuleForm.vehicle"> {{ $t('cameraPoints.vehicleDetection') }}</label></li>
                        </ul>
                    </form>
                    <div class="config-action-buttons">
                        <button @click="toggleDrawingMode" class="drawing-btn">
                            {{ isDrawingMode ? $t('cameraPoints.cancelDrawing') : $t('cameraPoints.setRecognitionArea') }}
                        </button>
                        <button @click="clearCurrentRegion" class="clear-btn" v-if="currentPoints.length > 0">
                            {{ $t('cameraPoints.clearCurrentArea') }}
                        </button>
                        <button @click="saveAndCloseModal" class="save-btn">{{ $t('cameraPoints.saveCurrentConfig') }}</button>
                    </div>

                    <h3 class="control-header">{{ $t('cameraPoints.remoteControlAndVoice') }}</h3>
                    <div class="remote-controls">
                        <div class="command-sender">
                            <input type="text" :placeholder="$t('cameraPoints.inputVoiceContent')">
                            <button>{{ $t('cameraPoints.send') }}</button>
                        </div>
                        <div class="action-buttons">
                            <button :title="$t('cameraPoints.realTimeIntercom')">🎤</button>
                            <button>{{ $t('cameraPoints.loopPlayback') }}</button>
                        </div>
                        <div class="volume-control">
                            <span class="volume-icon" @click="toggleMute">{{ volumeIcon }}</span>
                            <div class="volume-slider-container">
                                <input type="range" class="volume-slider" min="0" max="100" v-model="volume">
                                <span class="volume-value">{{ volume }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { storeToRefs } from 'pinia';
import { useCameraStore } from '@/stores/cameraStore';
import { subscribeToStream, sendPtzCommand, getCameraPresets, createCameraPreset, deleteCameraPreset, deleteAllCameraPresets, type Camera, type PtzCommand, type CameraPreset, type CameraPresetCreate } from '@/services/cameraService';
import CameraControlPanel from '@/components/CameraControlPanel.vue';

const { t: $t } = useI18n();
const cameraStore = useCameraStore();
const { sites, allCameras } = storeToRefs(cameraStore);

// --- WebRTC and Video Logic ---
const videoElements = ref<Record<number, HTMLVideoElement | null>>({});
const modalVideoRef = ref<HTMLVideoElement | null>(null);
const activeStreams = ref<Record<number, RTCPeerConnection | null>>({});
const statusText = ref('连接中...');
const statusColor = ref('connecting');

async function startStream(camera: Camera, videoEl: HTMLVideoElement | null) {
    if (!videoEl) {
        console.error("Video element is not available for camera:", camera.id);
        return;
    }
    if (camera.status !== '在线') {
        console.warn(`摄像头 ${camera.name} 当前离线`);
        videoEl.srcObject = null;
        return;
    }

    if (activeStreams.value[camera.id]) {
        activeStreams.value[camera.id]?.close();
    }

    console.log(`Starting WebRTC stream for camera ${camera.id}...`);
    statusText.value = '连接中...';
    statusColor.value = 'connecting';

    try {
        const pc = new RTCPeerConnection();
        activeStreams.value[camera.id] = pc;

        pc.ontrack = (event) => {
            console.log(`Track received for camera ${camera.id}`);
            videoEl.srcObject = event.streams[0];
            statusText.value = '已连接';
            statusColor.value = 'connected';
        };
        
        pc.onconnectionstatechange = () => {
            if(pc) {
                console.log(`Connection state for camera ${camera.id}: ${pc.connectionState}`);
                if (pc.connectionState === 'failed' || pc.connectionState === 'disconnected' || pc.connectionState === 'closed') {
                    statusText.value = '连接断开';
                    statusColor.value = 'error';
                    activeStreams.value[camera.id] = null;
                }
            }
        };

        const offer = await pc.createOffer();
        await pc.setLocalDescription(offer);

        const answer = await subscribeToStream(camera.id, offer);
        await pc.setRemoteDescription(answer);

    } catch (error) {
        console.error("WebRTC connection failed:", error);
        statusText.value = '连接失败';
        statusColor.value = 'error';
    }
}

// --- PTZ Control Logic ---
// PTZ命令执行的定时器管理
const ptzTimers = ref<Map<number, number>>(new Map());

async function handleDirectionEvent(direction: string) {
  if (!selectedCamera.value) {
    console.warn('没有选择摄像头，跳过PTZ命令');
    return;
  }

  const cameraId = selectedCamera.value.id;
  
  // 清除该摄像头之前的定时器，避免重复命令
  const existingTimer = ptzTimers.value.get(cameraId);
  if (existingTimer) {
    clearTimeout(existingTimer);
    ptzTimers.value.delete(cameraId);
  }

  const ptzCommandMap: { [key: string]: string } = {
    'up': 'TILT_UP',
    'down': 'TILT_DOWN',
    'left': 'PAN_LEFT',
    'right': 'PAN_RIGHT',
    'up-left': 'PAN_LEFT_TILT_UP',
    'up-right': 'PAN_RIGHT_TILT_UP', 
    'down-left': 'PAN_LEFT_TILT_DOWN',
    'down-right': 'PAN_RIGHT_TILT_DOWN',
    'center': 'STOP',
    'zoom-in': 'ZOOM_IN',
    'zoom-out': 'ZOOM_OUT',
  };

  const command = ptzCommandMap[direction];
  if (!command) {
    console.warn(`未知的PTZ方向: ${direction}`);
    return;
  }

  try {
    const ptzPayload = { command, speed: 50 };
    console.log(`发送PTZ命令: ${command} 到摄像头 ${cameraId}`);
    await sendPtzCommand(cameraId, ptzPayload);

    // 只有非STOP命令才需要自动停止
    if (command !== 'STOP') {
      const timer = setTimeout(async () => {
        try {
          console.log(`自动停止PTZ命令，摄像头 ${cameraId}`);
          await sendPtzCommand(cameraId, { command: 'STOP' });
        } catch (error) {
          console.error('自动停止PTZ命令失败:', error);
        } finally {
          ptzTimers.value.delete(cameraId);
        }
      }, 300);
      
      ptzTimers.value.set(cameraId, timer);
    }
  } catch (error) {
    console.error('PTZ命令执行失败:', error);
  }
}

// --- Data & State ---
// 用于存储每个工地的展开状态
const siteExpandedState = ref<Record<number, boolean>>({});

const treeData = computed(() => {
    const siteMap: { [key: number]: { id: number, name: string, cameras: Camera[], expanded: boolean } } = {};
    
    // 初始化所有工地
    sites.value.forEach(site => {
        // 如果还没有展开状态记录，则默认为展开
        if (!(site.id in siteExpandedState.value)) {
            siteExpandedState.value[site.id] = true;
        }
        siteMap[site.id] = {
            id: site.id,
            name: site.name,
            cameras: [],
            expanded: siteExpandedState.value[site.id]
        };
    });
    
    // 将摄像头分配到对应的工地
    allCameras.value.forEach(cam => {
        if (cam.site_id && siteMap[cam.site_id]) {
            siteMap[cam.site_id].cameras.push(cam);
        } else {
            // 处理没有工地关联的摄像头
            const unknownSiteId = -1;
            if (!siteMap[unknownSiteId]) {
                // 如果还没有展开状态记录，则默认为展开
                if (!(unknownSiteId in siteExpandedState.value)) {
                    siteExpandedState.value[unknownSiteId] = true;
                }
                siteMap[unknownSiteId] = {
                    id: unknownSiteId,
                    name: '未分配工地',
                    cameras: [],
                    expanded: siteExpandedState.value[unknownSiteId]
                };
            }
            siteMap[unknownSiteId].cameras.push(cam);
        }
    });
    
    // 返回有摄像头的工地列表，按工地名称排序
    return Object.values(siteMap)
        .filter(site => site.cameras.length > 0)
        .sort((a, b) => {
            // 未分配工地排在最后
            if (a.id === -1) return 1;
            if (b.id === -1) return -1;
            return a.name.localeCompare(b.name);
        });
});

// --- 统计信息 ---
const siteStats = computed(() => {
    const totalSites = sites.value.length;
    const totalCameras = allCameras.value.length;
    const onlineCameras = allCameras.value.filter(cam => cam.status === '在线').length;
    
    return {
        totalSites,
        totalCameras,
        onlineCameras
    };
});

const currentGridSize = ref(16);
const currentPage = ref(1);
const activeCameraId = ref<number | null>(null);
const highlightedCameraId = ref<number | null>(null);
const isModalVisible = ref(false);
const selectedCamera = ref<Camera | null>(null);

// --- 弹窗相关变量 ---
const presets = ref<CameraPreset[]>([]);
const alarmRuleForm = ref({ person: false, helmet: false, vehicle: false });
const isDrawingMode = ref(false);
const currentPoints = ref<Array<{ x: number; y: number }>>([]);
const volume = ref(50);
const volumeIcon = computed(() => volume.value === 0 ? '🔇' : '🔊');
const canvasRef = ref<HTMLCanvasElement | null>(null);

// --- Computed Properties ---
const totalPages = computed(() => Math.ceil(allCameras.value.length / currentGridSize.value));
const paginatedCameras = computed(() => {
  const startIndex = (currentPage.value - 1) * currentGridSize.value;
  const endIndex = startIndex + currentGridSize.value;
  return allCameras.value.slice(startIndex, endIndex);
});

// --- Methods ---
function setGridSize(size: number) {
  currentGridSize.value = size;
  currentPage.value = 1;
}

function prevPage() {
  if (currentPage.value > 1) currentPage.value--;
}

function nextPage() {
  if (currentPage.value < totalPages.value) currentPage.value++;
}

function toggleArea(site: { id: number; name: string; cameras: Camera[]; expanded: boolean }) {
  // 切换展开状态
  siteExpandedState.value[site.id] = !siteExpandedState.value[site.id];
  console.log(`切换工地 ${site.name} (编号: ${site.id}) 的展开状态为: ${siteExpandedState.value[site.id]}`);
}

// 获取指定工地的在线摄像头数量
function getSiteOnlineCount(site: { cameras: Camera[] }): number {
  return site.cameras.filter(cam => cam.status === '在线').length;
}

function selectCamera(cam: Camera) {
    activeCameraId.value = cam.id;
    const cameraIndex = allCameras.value.findIndex(c => c.id === cam.id);
    if (cameraIndex === -1) return;

    const targetPage = Math.floor(cameraIndex / currentGridSize.value) + 1;
    if (currentPage.value !== targetPage) {
        currentPage.value = targetPage;
    }

    nextTick(() => {
        highlightedCameraId.value = cam.id;
        setTimeout(() => { highlightedCameraId.value = null; }, 2500);

        const videoEl = videoElements.value[cam.id];
        if (videoEl) {
            startStream(cam, videoEl);
        }
    });
}

async function openDetailModal(cam: Camera) {
  console.log('点击摄像头画面:', cam.id, cam.name);
  selectedCamera.value = cam;
  isModalVisible.value = true;
  console.log('弹窗可见性状态:', isModalVisible.value);
  
  // 加载摄像头预置点
  await loadCameraPresets(cam.id);
  
  await nextTick();
  if (modalVideoRef.value) {
      startStream(cam, modalVideoRef.value);
  }
}

function closeDetailModal() {
  const currentCameraId = selectedCamera.value?.id;
  isModalVisible.value = false;
  selectedCamera.value = null;
  
  // Clean up the stream when closing the modal
  const stream = modalVideoRef.value?.srcObject as MediaStream;
  if (stream && stream.getTracks) {
      stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
      modalVideoRef.value!.srcObject = null;
  }
  
  if (currentCameraId) {
    const pc = activeStreams.value[currentCameraId];
    if (pc) {
      pc.close();
      activeStreams.value[currentCameraId] = null;
    }
  }
}

// --- 弹窗模态相关函数 ---
// 加载摄像头预置点
async function loadCameraPresets(cameraId: number) {
  try {
    presets.value = await getCameraPresets(cameraId);
    console.log(`加载摄像头 ${cameraId} 预置点:`, presets.value);
  } catch (error) {
    console.error('加载预置点失败:', error);
    presets.value = [];
  }
}

function viewPlayback(camera: Camera) {
  console.log('查看回放:', camera.name);
  console.log(`正在打开摄像头 ${camera.name} 的回放功能`);
}

function addPreset() {
  if (!selectedCamera.value) return;
  
  const presetName = prompt('请输入预置位名称:');
  if (!presetName || presetName.trim() === '') return;
  
  // 调用异步函数添加预置点
  addPresetAsync(presetName.trim());
}

// 异步添加预置点
async function addPresetAsync(presetName: string) {
  if (!selectedCamera.value) return;
  
  try {
    // 模拟获取当前摄像头位置（实际应用中应从摄像头获取）
    const newPreset: Omit<CameraPresetCreate, 'camera_id'> = {
      name: presetName,
      pan: Math.random() * 360 - 180, // 模拟数据: -180 to 180
      tilt: Math.random() * 180 - 90,  // 模拟数据: -90 to 90
      zoom: Math.random() * 10 + 1     // 模拟数据: 1 to 11
    };
    
    const createdPreset = await createCameraPreset(selectedCamera.value.id, newPreset);
    presets.value.push(createdPreset);
    console.log('添加预置位成功:', createdPreset);
  } catch (error) {
    console.error('添加预置位失败:', error);
    alert('添加预置位失败，请检查名称是否重复');
  }
}

function readPreset() {
  if (!selectedCamera.value) return;
  console.log('读取当前预设位');
  console.log('已读取当前摄像头位置');
}

function applyPreset(preset: CameraPreset) {
  if (!selectedCamera.value) return;
  console.log('应用预置位:', preset.name);
  console.log(`正在应用预置位: ${preset.name} (Pan: ${preset.pan}, Tilt: ${preset.tilt}, Zoom: ${preset.zoom})`);
  // TODO: 实际应用中应调用PTZ命令将摄像头移动到指定位置
}

function deletePreset(presetId: number) {
  // 调用异步函数删除预置点
  deletePresetAsync(presetId);
}

// 异步删除预置点
async function deletePresetAsync(presetId: number) {
  const preset = presets.value.find(p => p.id === presetId);
  if (!preset) return;
  
  if (confirm(`确定要删除预置位 "${preset.name}" 吗?`)) {
    try {
      await deleteCameraPreset(presetId);
      const index = presets.value.findIndex(p => p.id === presetId);
      if (index !== -1) {
        presets.value.splice(index, 1);
      }
      console.log('删除预置位成功:', preset.name);
    } catch (error) {
      console.error('删除预置位失败:', error);
      alert('删除预置位失败');
    }
  }
}

// 删除全部预置点
function deleteAllPresets() {
  if (!selectedCamera.value) return;
  
  if (presets.value.length === 0) {
    console.log('当前摄像头没有预置点');
    return;
  }
  
  const confirmMessage = `确定要删除摄像头 "${selectedCamera.value.name}" 的全部 ${presets.value.length} 个预置点吗？\n\n此操作不可撤销！`;
  if (confirm(confirmMessage)) {
    deleteAllPresetsAsync();
  }
}

// 异步删除全部预置点
async function deleteAllPresetsAsync() {
  if (!selectedCamera.value) return;
  
  try {
    await deleteAllCameraPresets(selectedCamera.value.id);
    presets.value = []; // 清空本地预置点列表
    console.log(`成功删除摄像头 ${selectedCamera.value.name} 的全部预置点`);
  } catch (error) {
    console.error('删除全部预置点失败:', error);
    alert('删除全部预置点失败，请稍后重试');
  }
}

function toggleDrawingMode() {
  isDrawingMode.value = !isDrawingMode.value;
  console.log('切换绘制模式:', isDrawingMode.value);
  if (!isDrawingMode.value) {
    currentPoints.value = [];
  }
}

function clearCurrentRegion() {
  currentPoints.value = [];
  console.log('清除当前绘制区域');
}

function saveAndCloseModal() {
  console.log('保存配置并关闭弹窗');
  console.log('告警规则:', alarmRuleForm.value);
  console.log('绘制点:', currentPoints.value);
  console.log('配置已保存');
  closeDetailModal();
}

function toggleMute() {
  if (volume.value > 0) {
    volume.value = 0;
  } else {
    volume.value = 50;
  }
  console.log('切换静音状态, 音量:', volume.value);
}

// --- Lifecycle Hooks ---
onMounted(() => {
  cameraStore.fetchAllData();
});

// 清理资源和定时器
onUnmounted(() => {
  // 清理所有PTZ定时器
  ptzTimers.value.forEach((timer) => {
    clearTimeout(timer);
  });
  ptzTimers.value.clear();
  
  // 清理所有视频流
  Object.values(activeStreams.value).forEach((pc) => {
    if (pc) {
      pc.close();
    }
  });
});

</script>

<style scoped>
/* Highlight Animation */
.video-item.highlight {
    outline: 3px solid var(--accent-color);
    box-shadow: 0 0 15px var(--accent-color);
    transition: all 0.3s ease-in-out;
}


/* GLOBAL STYLES - ADAPTED FROM ORIGINAL style.css */
:root {
    --primary-color: #005a9c;
    --secondary-color: #007bff;
    --accent-color: #00bfff;
    --background-color: #f0f4f8;
    --light-background: #ffffff;
    --text-color: #333;
    --border-color: #e0e0e0;
    --alarm-color: #d9534f;
}

.camera-points-layout-wrapper {
  padding: 20px;
  background-color: var(--background-color);
  height: 100%;
}

.card {
  background-color: var(--light-background);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.card h2 {
    margin-top: 0;
    color: var(--primary-color);
    border-bottom: 2px solid var(--background-color);
    padding-bottom: 10px;
    font-size: 1.4em;
}

.camera-points-layout {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 20px;
  height: calc(100vh - 100px);
}

.tree-panel {
  overflow-y: auto;
  margin-bottom: 0;
}

.camera-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid var(--primary-color);
}

.stats-item {
  font-size: 14px;
  color: var(--text-color);
}

.stats-item strong {
  color: var(--primary-color);
  margin-right: 8px;
}

.camera-tree, .camera-tree ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.camera-tree ul {
    padding-left: 20px;
}

.camera-tree .tree-node {
    padding: 8px 10px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
    display: block;
}

.camera-tree .site-node {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: bold;
    padding: 10px 12px;
    position: relative;
    cursor: pointer;
    border-radius: 6px;
    transition: background-color 0.2s ease;
}

.camera-tree .site-node:hover {
    background-color: #f0f4f8;
}

.camera-tree .site-node::before {
    content: '▼';
    display: inline-block;
    transition: transform 0.2s;
    font-size: 12px;
    color: var(--primary-color);
    flex-shrink: 0;
}

.camera-tree .tree-parent.collapsed > .site-node::before {
    transform: rotate(-90deg);
}

.camera-tree .site-icon {
    font-size: 16px;
}

.camera-tree .site-name {
    flex: 1;
    color: var(--primary-color);
}

.camera-tree .camera-count {
    font-size: 12px;
    color: #666;
    font-weight: normal;
}

.camera-tree .online-indicator {
    font-size: 11px;
    color: #28a745;
    font-weight: normal;
}

.camera-tree .camera-node {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    font-weight: normal;
}

.camera-tree .camera-status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.camera-tree .camera-status-dot.在线 {
    background-color: #28a745;
}

.camera-tree .camera-status-dot.离线 {
    background-color: #dc3545;
}

.camera-tree .camera-status-dot.维修中 {
    background-color: #ffc107;
}

.camera-tree .camera-info {
    flex: 1;
    font-weight: 500;
}

.camera-tree .camera-location {
    font-size: 11px;
    color: #666;
    font-style: italic;
}

.camera-tree .tree-node:hover {
    background-color: var(--background-color);
}

.camera-tree .tree-node.active {
    background-color: var(--secondary-color);
    color: white;
}

.camera-tree .tree-parent.collapsed > .site-node::before {
    transform: rotate(-90deg);
}

.camera-tree .tree-parent.collapsed > ul {
    display: none;
}

.video-panel .card {
    margin-bottom: 0;
}

.controls { 
    margin-bottom: 20px; 
    display: flex;
    align-items: center;
    gap: 10px;
}

.controls span {
    font-weight: 500;
    color: #374151;
}

.controls button {
    padding: 6px 12px;
    border: 1px solid #1e3a8a;
    background-color: #ffffff;
    color: #1e3a8a;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.controls button:hover {
    background-color: #1e3a8a;
    color: #ffffff;
}

.controls button.active {
    background-color: #1e3a8a;
    color: #ffffff;
}

.video-grid {
  display: grid;
  gap: 10px;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-4 { grid-template-columns: repeat(2, 1fr); }
.grid-9 { grid-template-columns: repeat(3, 1fr); }
.grid-16 { grid-template-columns: repeat(4, 1fr); }

.video-item {
  position: relative;
  background-color: #000;
  aspect-ratio: 16 / 9;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
}

.video-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-item .overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0,0,0,0.5);
  color: white;
  padding: 5px 10px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
}

.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
  gap: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #d1d5db;
  background-color: #ffffff;
  color: #374151;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 80px;
}

.page-btn:hover:not(:disabled) {
  background-color: #f3f4f6;
  border-color: #9ca3af;
  color: #1f2937;
}

.page-btn:disabled {
  cursor: not-allowed;
  opacity: 0.5;
  background-color: #f9fafb;
  border-color: #e5e7eb;
  color: #9ca3af;
}

.page-info {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  padding: 8px 16px;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  min-width: 120px;
  text-align: center;
}

/* Modal Styles */
.modal {
  display: flex;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.6);
  align-items: center;
  justify-content: center;
}

.modal-content {
  background-color: #fefefe;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  width: 95vw;
  max-width: 1600px;
  display: flex;
  flex-direction: column;
  max-height: 95vh;
  position: relative;
}

.modal-content.large {
    max-width: 1450px;
    height: 90vh; /* Set a large, explicit height for the modal */
}

.close-btn {
    color: #aaa;
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
    z-index: 10;
}

.close-btn:hover {
    color: #333;
}

.modal-header {
    padding: 15px 25px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.title-with-restart {
    display: flex;
    align-items: center;
    gap: 20px;
}

.modal-header h2 {
    margin: 0;
    color: #1e3a8a;
    font-size: 20px;
    font-weight: 600;
}

.restart-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    font-size: 14px;
    background-color: #fff0f0;
    color: #ff4d4f;
    border: 1px solid #ffccc7;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
}

.restart-btn:hover {
    background-color: #ff4d4f;
    color: #fff;
}

.modal-body {
    display: flex;
    gap: 20px;
    padding: 25px;
    overflow: hidden;
    flex-grow: 1;
}

.left-panel {
    flex: 2;
    display: flex;
    flex-direction: column; /* Stack video and buttons vertically */
    gap: 15px; /* Space between video and buttons */
    min-width: 0;
}

.video-wrapper {
    flex-grow: 1; /* Allow video to take all available vertical space */
    position: relative;
    background-color: #000;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 0; /* Required for flex-grow to work correctly */
}

.video-wrapper img {
    width: 100%;
    height: 100%;
    object-fit: cover; /* Changed from contain to cover */
}

.drawing-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: auto;
    z-index: 10;
}

.video-control-buttons {
    display: flex;
    justify-content: center; /* Center the buttons horizontally */
    gap: 10px;
    flex-shrink: 0; /* Prevent buttons from shrinking */
}

.video-btn {
    padding: 8px 16px; /* Increased padding for a better look */
    border: 1px solid; /* Set a default border */
    border-radius: 6px;
    color: var(--text-color);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s;
    background-color: #fff;
}

.video-btn .btn-icon {
    font-size: 1.1em;
}

/* Record Button */
.record-btn {
    border-color: #ff4d4f;
    color: #ff4d4f;
}
.record-btn:hover {
    background-color: #ff4d4f;
    color: #fff;
}

/* Stop Button */
.stop-btn {
    border-color: #28a745;
    color: #28a745;
}
.stop-btn:hover:not(:disabled) {
    background-color: #28a745;
    color: #fff;
}

/* Playback Button */
.playback-btn {
    border-color: #6f42c1;
    color: #6f42c1;
}
.playback-btn:hover {
    background-color: #6f42c1;
    color: #fff;
}

/* Disabled State */
.video-btn:disabled {
    background-color: #f8f8f8;
    border-color: #ccc;
    color: #aaa;
    cursor: not-allowed;
}


.config-detail-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow-y: auto;
    padding-right: 15px;
    min-height: 0;
}

.preset-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.preset-list {
    margin-top: 20px;
    border-top: 1px solid #e5e7eb;
    padding-top: 15px;
}
.preset-list h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 15px;
    color: #374151;
    font-weight: 600;
}
.preset-list ul {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 150px; /* Or adjust as needed */
    overflow-y: auto;
}
.preset-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}
.preset-list li:hover {
    background-color: #f3f4f6;
}
.preset-list li span {
    font-size: 14px;
}
.preset-actions button {
    margin-left: 8px;
    padding: 3px 8px;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
}
.apply-btn {
    background-color: #e0f2fe;
    border: 1px solid #7dd3fc;
    color: #0c4a6e;
}
.delete-btn {
    background-color: #fee2e2;
    border: 1px solid #fca5a5;
    color: #991b1b;
}

.ptz-actions {
    display: flex;
    justify-content: center;
    margin-top: -10px; /* Pull it closer to the control pad */
    margin-bottom: 15px;
}
.ptz-action-btn {
    padding: 8px 20px;
    font-size: 14px;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}
.ptz-action-btn:hover {
    background-color: #e5e7eb;
}


.preset-btn {
    flex-grow: 1;
    padding: 10px;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border: 2px solid #1e3a8a;
    border-radius: 8px;
    background-color: #ffffff;
    color: #1e3a8a;
    cursor: pointer;
    transition: all 0.2s ease;
}

.preset-btn:hover {
    background-color: #f8f9fa;
    border-color: #1e40af;
    color: #1e40af;
}

.delete-all-btn {
    border-color: #dc3545;
    color: #dc3545;
}

.delete-all-btn:hover {
    background-color: #f8d7da;
    border-color: #721c24;
    color: #721c24;
}

.config-detail-container h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1e3a8a;
    margin-top: 10px;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #e5e7eb;
}

.config-form ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.config-form li label {
    font-size: 14px;
    color: #374151;
}

.config-form input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

.config-action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.config-action-buttons button {
    flex: 1;
    min-width: 120px;
    padding: 10px 15px;
    border: 2px solid #1e3a8a;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #ffffff;
    color: #1e3a8a;
}

.config-action-buttons button:hover {
    background-color: #f8f9fa;
    border-color: #1e40af;
    color: #1e40af;
}

.drawing-btn {
    background-color: #ffffff;
    color: #1e3a8a;
    border-color: #1e3a8a;
}

.drawing-btn:hover {
    background-color: #f8f9fa;
    border-color: #1e40af;
    color: #1e40af;
}

.clear-btn {
    background-color: #ffffff;
    color: #1e3a8a;
    border-color: #1e3a8a;
}

.clear-btn:hover {
    background-color: #f8f9fa;
    border-color: #1e40af;
    color: #1e40af;
}

.save-btn {
    background-color: #ffffff;
    color: #1e3a8a;
    border-color: #1e3a8a;
}

.save-btn:hover {
    background-color: #f8f9fa;
    border-color: #1e40af;
    color: #1e40af;
}

.drawing-status {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.status-dot.drawing {
    background-color: #007bff;
    animation: pulse 2s infinite;
}

.status-text {
    font-weight: 500;
    color: #495057;
}

.drawing-info {
    display: flex;
    gap: 15px;
    font-size: 14px;
    color: #6c757d;
}

.warning {
    color: #dc3545;
    font-weight: 500;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.control-header {
    margin-top: 10px;
    border-top: 1px solid #f0f0f0;
    padding-top: 20px;
}

.remote-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.remote-controls .command-sender {
    display: flex;
    flex-grow: 1;
    gap: 10px;
}
.remote-controls input {
    flex-grow: 1;
    padding: 10px 15px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background-color: #ffffff;
    color: #374151;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s ease;
}

.remote-controls input:focus {
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.remote-controls input::placeholder {
    color: #9ca3af;
}

.remote-controls .command-sender button {
    padding: 10px 20px;
    border: 2px solid #1e3a8a;
    border-radius: 8px;
    background-color: #ffffff;
    color: #1e3a8a;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.remote-controls .command-sender button:hover {
    background-color: #f8f9fa;
    border-color: #1e40af;
    color: #1e40af;
}

.remote-controls .action-buttons button {
    font-size: 20px;
    padding: 8px 12px;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 5px;
}
.volume-icon {
    font-size: 24px;
    cursor: pointer;
}
.volume-slider-container {
    display: flex;
    align-items: center;
    gap: 8px;
}
.volume-slider {
    width: 100px;
}

/* Status text colors */
.status-overlay {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 14px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
}
.connecting { color: #fff; }
.connected { color: lightgreen; }
.error { color: #ff4d4f; }

/*
  FIX: Force the CameraControlPanel to be visible.
  A global style is incorrectly setting its display to 'none'.
  By adding a more specific rule here, we override the global style
  without needing to use !important.
*/
.config-detail-container .camera-control-panel {
  display: block;
  min-height: 400px; /* Ensure it has enough space to render */
}
</style>