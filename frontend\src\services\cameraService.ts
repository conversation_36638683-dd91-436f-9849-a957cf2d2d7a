// frontend/src/services/cameraService.ts

import axios from 'axios';

// --- Base API Client ---
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

// --- Interfaces ---

export interface Site {
  id: number;
  name: string;
  area: string | null;
  description: string | null;
}

export interface Camera {
  id: number;
  name: string;
  ip_address: string;
  port: number;
  rtsp_url?: string;
  status: '在线' | '离线' | '维修中';
  site_id: number;
  location?: string;
  latitude?: number;
  longitude?: number;
}

export interface CameraPreset {
  id: number;
  name: string;
  pan: number;
  tilt: number;
  zoom: number;
  camera_id: number;
}

export interface StreamURL {
  url: string;
}

export type SiteCreate = Omit<Site, 'id'>;
export type CameraCreate = Omit<Camera, 'id' | 'status' | 'rtsp_url'>;
export type CameraPresetCreate = Omit<CameraPreset, 'id'>;
export type CameraPresetUpdate = Partial<Omit<CameraPreset, 'id' | 'camera_id'>>;

// --- Service Functions ---

// Site API
export const getSites = async (): Promise<Site[]> => {
  const response = await apiClient.get('/sites/');
  return response.data;
};

export const createSite = async (siteData: SiteCreate): Promise<Site> => {
  const response = await apiClient.post('/sites/', siteData);
  return response.data;
};

export const deleteSite = async (id: number): Promise<void> => {
  await apiClient.delete(`/sites/${id}`);
};

// Camera API
export const getCameras = async (): Promise<Camera[]> => {
  const response = await apiClient.get('/cameras/');
  return response.data;
};

export const createCamera = async (cameraData: CameraCreate): Promise<Camera> => {
  const response = await apiClient.post('/cameras/', cameraData);
  return response.data;
};

export const deleteCamera = async (id: number): Promise<void> => {
  await apiClient.delete(`/cameras/${id}`);
};

// Live Stream API
export const getLiveStreamUrl = async (cameraId: number): Promise<StreamURL> => {
  const response = await apiClient.get(`/cameras/${cameraId}/live_url`);
  return response.data;
};

// Camera Presets API
export const getCameraPresets = async (cameraId: number): Promise<CameraPreset[]> => {
  const response = await apiClient.get(`/cameras/${cameraId}/presets/`);
  return response.data;
};

export const createCameraPreset = async (presetData: CameraPresetCreate): Promise<CameraPreset> => {
  const response = await apiClient.post(`/cameras/${presetData.camera_id}/presets/`, presetData);
  return response.data;
};

export const deleteCameraPreset = async (presetId: number): Promise<void> => {
  await apiClient.delete(`/presets/${presetId}`);
};
