// frontend/src/services/aiConfigService.ts

import axios from 'axios';

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  headers: {
    'Content-Type': 'application/json'
  }
});

export interface AiConfig {
  [className: string]: {
    enabled: boolean;
    confidence: number;
  };
}

export interface ModelClasses {
  [classId: string]: string;
}

/**
 * 获取当前加载的YOLO模型可以识别的所有类别。
 * @returns 一个从类别ID到类别名称的映射，例如 { "0": "person", "1": "car" }。
 */
export const getModelClasses = async (): Promise<ModelClasses> => {
  try {
    const response = await apiClient.get<ModelClasses>('/ai/model/classes');
    return response.data;
  } catch (error) {
    console.error('获取模型类别失败:', error);
    return {};
  }
};

/**
 * 获取当前的全局AI检测配置。
 * @returns 一个从类别名称到其配置的映射，例如 { "person": { "enabled": true, "confidence": 0.7 } }。
 */
export const getAiConfig = async (): Promise<AiConfig> => {
  try {
    const response = await apiClient.get<AiConfig>('/ai/config');
    return response.data;
  } catch (error) {
    console.error('获取AI配置失败:', error);
    return {};
  }
};

/**
 * 更新全局AI检测配置。
 * @param config - 新的配置对象。
 */
export const updateAiConfig = async (config: AiConfig): Promise<void> => {
  try {
    await apiClient.put('/ai/config', config);
  } catch (error) {
    console.error('更新AI配置失败:', error);
    // 在实际应用中，这里应该抛出错误，让调用者可以处理
    throw error;
  }
};
