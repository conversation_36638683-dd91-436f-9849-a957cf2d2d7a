# AI 视频处理模块设计方案 (v2 - 支持动态全局配置)

## 1. 核心架构：引入动态配置管理器

*   **`AIConfigManager` (新增模块):**
    *   创建一个新的单例类 `AIConfigManager`，位于 `backend/ai_config_manager.py`。
    *   **职责:** 在内存中维护一套全局的、线程安全的 AI 检测配置。这是所有 AI 处理任务共享的“唯一事实来源”。
    *   **配置内容:** 管理一个配置字典，结构如下：
      ```python
      {
          # 类别名称到配置的映射
          "person": {"enabled": True, "confidence": 0.7},
          "car": {"enabled": True, "confidence": 0.55},
          "helmet": {"enabled": False, "confidence": 0.8}, # 示例：暂时不检测安全帽
      }
      ```
    *   **初始化:** 服务器启动时，`AIConfigManager` 会从 `config.py` 加载一套初始默认配置。

*   **`AIProcessor` (职责调整):**
    *   `AIProcessor` 不再自己管理配置。
    *   在执行每一次推理过滤时，它会**实时地**从 `AIConfigManager` 单例中获取最新的检测配置，并应用到过滤逻辑中。这确保了配置的任何更改都能**立即生效**于所有正在运行的视频处理任务。

## 2. 动态配置的 API 接口

为了让前端能够读取和更新这套全局配置，需要两个新的 API 端点：

*   **`GET /api/ai/config`**
    *   **功能:** 读取并返回 `AIConfigManager` 中当前的完整 AI 配置。
    *   **前端用途:** 前端管理页面（例如 `ConfigView.vue`）可以使用此接口来获取并展示所有类别的当前状态（是否启用、置信度是多少）。

*   **`PUT /api/ai/config`**
    *   **功能:** 接收前端发来的新配置，并用它来更新 `AIConfigManager` 中的配置。
    *   **前端用途:** 当管理员在前端页面上修改了任何类别的置信度或启用了/禁用了某个类别并点击“保存”时，前端将调用此接口。
    *   **安全性:** 这个接口应被设置为需要管理员权限才能访问。

## 3. 推理流程 (已集成动态配置)

1.  `AIProcessor` 从视频流中获取一个批次的帧 (`frame_buffer`)。
2.  执行 `model.predict(frame_buffer)`，得到原始的、未经过滤的推理结果 `results`。
3.  **关键步骤:** 对于 `results` 中的每一帧的每一个检测框 (`box`):
    a.  获取其类别名称 `class_name`。
    b.  **实时查询** `AIConfigManager.get_config(class_name)`，得到该类别当前的配置。
    c.  **应用过滤逻辑:**
        *   如果 `config["enabled"]` 为 `False`，**直接跳过**这个检测框。
        *   如果检测框的置信度 `< config["confidence"]`，**跳过**这个检测框。
    d.  只有通过了以上所有检查的检测框，才被视为“有效检测”。
4.  如果一帧中存在**至少一个**“有效检测”，则触发后续的事件保存逻辑（图片和JSON）。

## 4. 视频/图片存储优化 (保持不变)

*   **视频:** H.264 编码 (`avc1`)、固定码率 (`1500k`)。
*   **图片:** JPEG 质量压缩 (质量参数 `80`)。

## 5. JSON 数据结构与文件结构 (保持不变)

*   **JSON 结构:** 保持为前端回放页量身定制的详细结构，包含时间戳、偏移量、检测框等信息。
*   **目录结构:** 保持按 `output/<camera_id>/videos|detections` 的清晰结构。
