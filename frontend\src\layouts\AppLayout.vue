<template>
  <div class="app-layout">
    <div class="main-container">
      <TheSidebar />
      <main class="main-content">
        <div v-if="isLoading && !hasInitialData" class="loading-overlay">
          <div class="loading-spinner"></div>
          <p>正在加载核心数据，请稍候...</p>
        </div>
        <router-view v-show="!isLoading || hasInitialData" />
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { storeToRefs } from 'pinia';
import TheSidebar from '@/components/TheSidebar.vue';
import { useCameraStore } from '@/stores/cameraStore';

const cameraStore = useCameraStore();
// 只需获取状态，不再触发 action
const { isLoading, cameras, sites } = storeToRefs(cameraStore);

// 检查是否已有初始数据，用于显示加载动画
const hasInitialData = computed(() => {
  return cameras.value.length > 0 || sites.value.length > 0;
});

// onMounted 中的 fetchAllData 已被移除
</script>

<style scoped>
.app-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.main-container {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  position: relative; /* For loading overlay positioning */
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.loading-spinner {
  border: 4px solid #f3f3f3; /* Light grey */
  border-top: 4px solid #3498db; /* Blue */
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
