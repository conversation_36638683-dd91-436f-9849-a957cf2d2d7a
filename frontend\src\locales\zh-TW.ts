export default {
  // 通用
  common: {
    save: '儲存',
    cancel: '取消',
    confirm: '確認',
    delete: '刪除',
    edit: '編輯',
    add: '新增',
    search: '搜尋',
    refresh: '重新整理',
    loading: '載入中...',
    success: '成功',
    error: '錯誤',
    warning: '警告',
    info: '資訊'
  },

  // 導航
  navigation: {
    systemTitle: '智慧視訊監控系統',
    home: '監控主頁',
    cameraPoints: '攝影機點位',
    playback: '監控回放',
    resourceManagement: '資源管理',
    alarmManagement: '告警事件管理',
    cameraManagement: '攝影機管理',
    config: '系統配置',
    alarms: '報警記錄',
    cameras: '攝影機管理',
    about: '關於系統'
  },

  // 管理頁面
  management: {
    pageTitle: '攝影機管理',
    siteManagement: '工地管理',
    cameraManagement: '攝影機管理'
  },

  // 首頁
  home: {
    cameraDistribution: '攝影機分佈概覽',
    latestAlarms: '最新報警資訊',
    alarmCount: '報警數量',
    unknownSite: '未知工地',
    alarmDetails: '報警詳情',
    alarmDescription: '在 {location} 於 {time} 發生。',
    viewDetails: '查看詳情',
    clickToView: '點擊查看詳情',
    
    // 報警類型
    alarmTypes: {
      personForbidden: '施工人員禁區作業',
      vehicleForbidden: '工程車輛禁區作業',
      personIntrusion: '施工人員違規闖入',
      vehicleIntrusion: '工程車輛違規闖入'
    },
    
    // 攝影機位置
    cameraLocations: {
      materialArea: '2號攝影機：材料區',
      entrance: '1號攝影機：工地入口',
      craneArea: '3號攝影機：塔吊作業區'
    },
    
    // 時間相關
    timeAgo: {
      twoMinutes: '2分鐘前',
      fiveMinutes: '5分鐘前',
      twelveMinutes: '12分鐘前',
      thirtyMinutes: '30分鐘前'
    },
    
    // 攝影機資訊
    cameraInfo: {
      id: '攝影機ID',
      status: '狀態',
      coordinates: '座標',
      location: '位置'
    },
    
    // 攝影機狀態
    cameraStatus: {
      online: '線上',
      offline: '離線'
    },
    
    // 圖表標籤
    chartLabels: {
      todayAlarms: '今日報警次數',
      fourDayTotal: '近4日報警總量'
    }
  },

  // 攝影機點位頁面
  cameraPoints: {
    cameraList: '攝影機列表',
    realTimeVideo: '攝影機即時畫面',
    gridMode: '分屏模式',
    previousPage: '上一頁',
    nextPage: '下一頁',
    pageInfo: '第 {current} / {total} 頁',
    restartCamera: '重啟攝影機',
    restart: '重啟',
    viewPlayback: '查看回放',
    addPreset: '添加預置點',
    readPreset: '讀取預置點',
    savedPresets: '已存預置點',
    apply: '應用',
    delete: '刪除',
    cameraTitle: '{id}號攝影機: {name}',
    applyPresetFailed: '應用預置點失敗：無效的角度值。',
    confirmDeletePreset: '確定要刪除這個預置點嗎?',
    
    // 報警規則配置
    alarmRulesConfig: '報警規則配置',
    personDetection: '識別區域內出現施工人員',
    helmetDetection: '識別人員未佩戴安全帽',
    vehicleDetection: '識別工程車輛',
    
    // 識別區域繪製
    cancelDrawing: '取消繪製',
    setRecognitionArea: '設定/重設識別區域',
    clearCurrentArea: '清除當前區域',
    saveCurrentConfig: '儲存當前配置',
    recognitionAreaCompleted: '識別區域繪製完成！\n\n區域已自動閉合，請點擊"儲存當前配置"來儲存設定。',
    drawingCancelled: '已取消繪製，未完成的區域已清除',
    drawingModeEnabled: '繪製模式已開啟 - 請在畫面上點擊定義識別區域',
    pointsDrawn: '已繪製 {count} 個點',
    minimumPointsRequired: '至少需要3個點',
    
    // 遠端控制與喊話
    remoteControlAndVoice: '遠端控制與喊話',
    inputVoiceContent: '輸入喊話內容...',
    send: '發送',
    realTimeIntercom: '即時對講',
    loopPlayback: '循環播放'
  },

  // 攝影機控制面板
  cameraControl: {
    panTiltOperation: '雲台操作',
    reset: '重置',
    zoom: '調焦',
    focus: '聚焦',
    aperture: '光圈',
    zoomLevel: '變倍',
    resetSuccess: '攝影機參數已重置為預設值'
  },

  // 回放頁面
  playback: {
    playbackControl: '回放控制',
    selectCamera: '選擇攝影機',
    pleaseSelectCamera: '請選擇攝影機',
    cameraTitle: '{id}號攝影機：{name}',
    selectTime: '選擇時間',
    playbackControls: '回放控制',
    play: '播放',
    pause: '暫停',
    stop: '停止',
    playbackSpeed: '播放速度',
    timeline: '時間軸',
    operations: '操作',
    screenshot: '截圖',
    exportClip: '匯出片段',
    videoPlayback: '影片回放',
    eventMarkers: '事件標記',
    jump: '跳轉',
    nowPlaying: '正在播放...',
    paused: '已暫停',
    pleaseSelectToStart: '請選擇攝影機和時間開始回放',
    pleaseSelectCameraFirst: '請先選擇攝影機！',
    pleaseSelectDate: '請選擇日期！',
    pleaseStartPlaybackFirst: '請先開始播放！',
    screenshotFunction: '截圖功能：\n\n已儲存當前幀截圖\n檔案名：{filename}',
    enterExportStartTime: '請輸入匯出開始時間 (格式: HH:MM:SS):',
    enterExportEndTime: '請輸入匯出結束時間 (格式: HH:MM:SS):',
    exportFunction: '匯出功能：\n\n正在匯出影片片段\n時間範圍：{startTime} - {endTime}',
    eventTypes: {
      personForbidden: '施工人員禁區作業',
      vehicleForbidden: '工程車輛違規進入',
      noHelmet: '施工人員未佩戴安全帽'
    }
  },

  // 報警記錄頁面
  alarms: {
    title: '報警記錄管理',
    totalCount: '總報警數量',
    todayCount: '今日報警數量',
    status: {
      unconfirmed: '未確認',
      confirmed: '已確認',
      falseAlarm: '誤報'
    },
    actions: {
      confirm: '確認報警',
      markFalse: '標記誤報',
      edit: '編輯記錄',
      delete: '刪除記錄'
    },
    filters: {
      search: '搜尋報警記錄...',
      status: '狀態篩選',
      type: '類型篩選'
    },
    table: {
      id: 'ID',
      time: '時間',
      type: '類型',
      location: '位置',
      screenshot: '截圖',
      status: '狀態',
      handler: '處理人',
      actions: '操作'
    }
  },

  // 設定頁面
  config: {
    // 報警規則設定
    alarmRules: {
      title: '攝影機全域報警規則設定',
      personDetection: '識別區域內出現施工人員',
      helmetDetection: '識別人員未佩戴安全帽',
      vehicleDetection: '識別工程車輛'
    },

    // 儲存設定
    storage: {
      title: '儲存位置設定',
      imagePath: '圖片儲存路徑',
      videoPath: '錄影儲存路徑',
      retentionDays: '儲存保留天數',
      days: '{count}天',
      autoCleanup: '自動清理',
      enabled: '開啟',
      disabled: '關閉',
      testPath: '測試儲存路徑',
      pathHint: '支援相對/絕對路徑，建議使用絕對路徑',
      videoHint: '建議使用大容量儲存，支援網路路徑'
    },

    // 郵件設定
    email: {
      title: '郵件發送設定',
      dailyEmail: '每日郵件發送',
      sendTime: '發送時間',
      recipients: '收件人郵箱',
      subject: '郵件主旨',
      content: '郵件內容',
      recipientsHint: '請輸入郵箱地址，多個郵箱用逗號分隔',
      subjectHint: '請輸入郵件主旨',
      contentHint: '請輸入郵件內容',
      saveConfig: '儲存郵件設定',
      refreshData: '重新整理今日資料',
      sendTest: '發送測試郵件'
    }
  },

  // 郵件內容範本
  emailTemplate: {
    greeting: '您好！以下是{date}監控系統的報警總結：',
    statistics: '📊 今日報警統計',
    totalAlarms: '• 總報警數量：{count}條',
    typeStats: '• {type}：{count}條',
    details: '📋 詳細報警記錄',
    recordFormat: '{index}. {time} {type} {location}',
    imageLink: '圖片連結：{url}'
  },

  // 時間相關
  time: {
    today: '今日',
    yesterday: '昨日',
    thisWeek: '本週',
    thisMonth: '本月',
    thisYear: '今年'
  },

  // 狀態訊息
  messages: {
    configSaved: '設定已儲存！',
    storageTestPassed: '✅ 儲存路徑測試通過！',
    emailConfigSaved: '郵件設定已儲存！',
    emailSentSuccess: '✅ 測試郵件發送成功！',
    emailSentFailed: '❌ 測試郵件發送失敗: {error}',
    emailjsConfigIncomplete: 'EmailJS 設定資訊不完整，請檢查 .env.local 檔案。',
    sendingTestEmail: '正在發送測試郵件...',
    testingStoragePath: '正在測試儲存路徑...',
    dataRefreshSuccess: '✅ 今日報警資料載入成功，郵件內容已更新',
    dataRefreshFailed: '取得今日報警資料失敗，請檢查後端服務',
    networkError: '取得今日報警資料出錯，請檢查網路連線'
  }
} 