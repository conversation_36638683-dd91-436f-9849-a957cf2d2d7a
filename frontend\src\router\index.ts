import { createRouter, createWebHistory } from 'vue-router'
import AppLayout from '@/layouts/AppLayout.vue'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: AppLayout,
      children: [
        {
          path: '',
          name: 'dashboard',
          component: HomeView,
          meta: { title: '仪表盘' }
        },
        {
          path: '/cameras',
          name: 'cameras',
          component: () => import('../views/ManagementView.vue'),
          meta: { title: '摄像头网格' }
        },
        {
          path: '/playback',
          name: 'playback',
          component: () => import('../views/PlaybackView.vue'),
          meta: { title: '监控回放' }
        },
        {
          path: '/alarms',
          name: 'alarms',
          component: () => import('../views/AlarmsView.vue'),
          meta: { title: '告警管理' }
        },
        {
          path: '/camera-points',
          name: 'camera-points',
          component: () => import('../views/CameraPointsView.vue'),
          meta: { title: '点位管理' }
        },
        {
          path: '/sites',
          name: 'sites',
          component: () => import('../views/ManagementView.vue'),
          meta: { title: '工地管理' }
        },
        {
          path: '/management',
          name: 'management',
          component: () => import('../views/ManagementView.vue'),
          meta: { title: '摄像头管理' }
        },
        {
          path: '/config',
          name: 'config',
          component: () => import('../views/ConfigView.vue'),
          meta: { title: '系统配置' }
        }
      ]
    }
  ]
})

export default router
