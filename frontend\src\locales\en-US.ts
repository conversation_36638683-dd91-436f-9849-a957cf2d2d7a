export default {
  // Common
  common: {
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    refresh: 'Refresh',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info'
  },

  // 导航
  navigation: {
    systemTitle: 'Smart Video Surveillance System',
    home: 'Monitoring Home',
    cameraPoints: 'Camera Points',
    playback: 'Video Playback',
    resourceManagement: 'Resource Management',
    alarmManagement: 'Alarm Event Management',
    cameraManagement: 'Camera Management',
    config: 'System Configuration',
    alarms: 'Alarm Records',
    cameras: 'Camera Management',
    about: 'About System'
  },

  // Management page
  management: {
    pageTitle: 'Camera Management',
    siteManagement: 'Site Management',
    cameraManagement: 'Camera Management'
  },

  // 首页
  home: {
    cameraDistribution: 'Camera Distribution Overview',
    latestAlarms: 'Latest Alarm Information',
    alarmCount: 'Alarm Count',
    unknownSite: 'Unknown Site',
    alarmDetails: 'Alarm Details',
    alarmDescription: 'Occurred at {location} at {time}.',
    viewDetails: 'View Details',
    clickToView: 'Click to view details',
    
    // 报警类型
    alarmTypes: {
      personForbidden: 'Construction Personnel in Restricted Area',
      vehicleForbidden: 'Construction Vehicle in Restricted Area',
      personIntrusion: 'Unauthorized Personnel Intrusion',
      vehicleIntrusion: 'Unauthorized Vehicle Intrusion'
    },
    
    // 摄像头位置
    cameraLocations: {
      materialArea: 'Camera 2: Material Area',
      entrance: 'Camera 1: Site Entrance',
      craneArea: 'Camera 3: Crane Operation Area'
    },
    
    // 时间相关
    timeAgo: {
      twoMinutes: '2 minutes ago',
      fiveMinutes: '5 minutes ago',
      twelveMinutes: '12 minutes ago',
      thirtyMinutes: '30 minutes ago'
    },
    
    // 摄像头信息
    cameraInfo: {
      id: 'Camera ID',
      status: 'Status',
      coordinates: 'Coordinates',
      location: 'Location'
    },
    
    // 摄像头状态
    cameraStatus: {
      online: 'Online',
      offline: 'Offline'
    },
    
    // 图表标签
    chartLabels: {
      todayAlarms: 'Today\'s Alarm Count',
      fourDayTotal: '4-Day Total Alarms'
    }
  },

  // 摄像头点位页面
  cameraPoints: {
    cameraList: 'Camera List',
    realTimeVideo: 'Camera Real-time Video',
    gridMode: 'Grid Mode',
    previousPage: 'Previous',
    nextPage: 'Next',
    pageInfo: 'Page {current} / {total}',
    restartCamera: 'Restart Camera',
    restart: 'Restart',
    viewPlayback: 'View Playback',
    addPreset: 'Add Preset',
    readPreset: 'Read Preset',
    deleteAllPresets: 'Delete All Presets',
    savedPresets: 'Saved Presets',
    apply: 'Apply',
    delete: 'Delete',
    cameraTitle: 'Camera {id}: {name}',
    applyPresetFailed: 'Failed to apply preset: Invalid angle values.',
    confirmDeletePreset: 'Are you sure you want to delete this preset?',
    
    // 报警规则配置
    alarmRulesConfig: 'Alarm Rules Configuration',
    personDetection: 'Detect construction personnel in restricted areas',
    helmetDetection: 'Detect personnel without safety helmets',
    vehicleDetection: 'Detect construction vehicles',
    
    // 识别区域绘制
    cancelDrawing: 'Cancel Drawing',
    setRecognitionArea: 'Set/Reset Recognition Area',
    clearCurrentArea: 'Clear Current Area',
    saveCurrentConfig: 'Save Current Configuration',
    recognitionAreaCompleted: 'Recognition area drawing completed!\n\nThe area has been automatically closed. Please click "Save Current Configuration" to save the settings.',
    drawingCancelled: 'Drawing cancelled, incomplete area has been cleared',
    drawingModeEnabled: 'Drawing mode enabled - Please click on the screen to define recognition area',
    pointsDrawn: '{count} points drawn',
    minimumPointsRequired: 'At least 3 points required',
    
    // 远程控制与喊话
    remoteControlAndVoice: 'Remote Control and Voice',
    inputVoiceContent: 'Enter voice content...',
    send: 'Send',
    realTimeIntercom: 'Real-time Intercom',
    loopPlayback: 'Loop Playback'
  },

  // 摄像头控制面板
  cameraControl: {
    panTiltOperation: 'Pan-Tilt Operation',
    reset: 'Reset',
    zoom: 'Zoom',
    focus: 'Focus',
    aperture: 'Aperture',
    zoomLevel: 'Zoom Level',
    resetSuccess: 'Camera parameters have been reset to default values'
  },

  // Playback page
  playback: {
    playbackControl: 'Playback Control',
    selectCamera: 'Select Camera',
    pleaseSelectCamera: 'Please select camera',
    cameraTitle: 'Camera {id}: {name}',
    selectTime: 'Select Time',
    playbackControls: 'Playback Controls',
    play: 'Play',
    pause: 'Pause',
    stop: 'Stop',
    playbackSpeed: 'Playback Speed',
    timeline: 'Timeline',
    operations: 'Operations',
    screenshot: 'Screenshot',
    exportClip: 'Export Clip',
    videoPlayback: 'Video Playback',
    eventMarkers: 'Event Markers',
    jump: 'Jump',
    nowPlaying: 'Now Playing...',
    paused: 'Paused',
    pleaseSelectToStart: 'Please select camera and time to start playback',
    pleaseSelectCameraFirst: 'Please select camera first!',
    pleaseSelectDate: 'Please select date!',
    pleaseStartPlaybackFirst: 'Please start playback first!',
    screenshotFunction: 'Screenshot function:\n\nCurrent frame screenshot saved\nFilename: {filename}',
    enterExportStartTime: 'Please enter export start time (format: HH:MM:SS):',
    enterExportEndTime: 'Please enter export end time (format: HH:MM:SS):',
    exportFunction: 'Export function:\n\nExporting video clip\nTime range: {startTime} - {endTime}',
    eventTypes: {
      personForbidden: 'Construction personnel in restricted area',
      vehicleForbidden: 'Construction vehicle violation',
      noHelmet: 'Personnel without safety helmet'
    }
  },

  // Alarms page
  alarms: {
    title: 'Alarm Record Management',
    totalCount: 'Total Alarm Count',
    todayCount: 'Today\'s Alarm Count',
    status: {
      unconfirmed: 'Unconfirmed',
      confirmed: 'Confirmed',
      falseAlarm: 'False Alarm'
    },
    actions: {
      confirm: 'Confirm Alarm',
      markFalse: 'Mark as False',
      edit: 'Edit Record',
      delete: 'Delete Record'
    },
    filters: {
      search: 'Search alarm records...',
      status: 'Status Filter',
      type: 'Type Filter'
    },
    table: {
      id: 'ID',
      time: 'Time',
      type: 'Type',
      location: 'Location',
      screenshot: 'Screenshot',
      status: 'Status',
      handler: 'Handler',
      actions: 'Actions'
    }
  },

  // Config page
  config: {
    // Alarm rules config
    alarmRules: {
      title: 'Camera Global Alarm Rules Configuration',
      personDetection: 'Detect construction personnel in restricted areas',
      helmetDetection: 'Detect personnel without safety helmets',
      vehicleDetection: 'Detect construction vehicles',
      confidence: 'Confidence Threshold',
      enabled: 'Enabled'
    },

    // Storage config
    storage: {
      title: 'Storage Location Configuration',
      imagePath: 'Image Storage Path',
      videoPath: 'Video Storage Path',
      retentionDays: 'Storage Retention Days',
      days: '{count} days',
      autoCleanup: 'Auto Cleanup',
      enabled: 'Enabled',
      disabled: 'Disabled',
      testPath: 'Test Storage Path',
      pathHint: 'Supports relative/absolute paths, recommend absolute paths',
      videoHint: 'Recommend high-capacity storage, supports network paths'
    },

    // Email config
    email: {
      title: 'Email Sending Configuration',
      dailyEmail: 'Daily Email Sending',
      sendTime: 'Send Time',
      recipients: 'Recipient Emails',
      subject: 'Email Subject',
      content: 'Email Content',
      recipientsHint: 'Enter email addresses, separate multiple emails with commas',
      subjectHint: 'Enter email subject',
      contentHint: 'Enter email content',
      saveConfig: 'Save Email Config',
      refreshData: 'Refresh Today\'s Data',
      sendTest: 'Send Test Email'
    }
  },

  // Email content template
  emailTemplate: {
    greeting: 'Hello! Here is the alarm summary for {date} monitoring system:',
    statistics: '📊 Today\'s Alarm Statistics',
    totalAlarms: '• Total alarm count: {count}',
    typeStats: '• {type}: {count}',
    details: '📋 Detailed Alarm Records',
    recordFormat: '{index}. {time} {type} {location}',
    imageLink: 'Image link: {url}'
  },

  // Time related
  time: {
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisYear: 'This Year'
  },

  // Status messages
  messages: {
    configSaved: 'Configuration saved!',
    storageTestPassed: '✅ Storage path test passed!',
    emailConfigSaved: 'Email configuration saved!',
    emailSentSuccess: '✅ Test email sent successfully!',
    emailSentFailed: '❌ Test email failed: {error}',
    emailjsConfigIncomplete: 'EmailJS configuration incomplete, please check .env.local file.',
    sendingTestEmail: 'Sending test email...',
    testingStoragePath: 'Testing storage path...',
    dataRefreshSuccess: '✅ Today\'s alarm data loaded successfully, email content updated',
    dataRefreshFailed: 'Failed to get alarm data, please check backend service',
    networkError: 'Error getting alarm data, please check network connection'
  }
} 