<template>
  <div class="container">
    <div class="playback-layout">
      <!-- Left Control Panel -->
      <div class="control-panel card">
        <h2>{{ $t('playback.playbackControl') }}</h2>
        
        <!-- Camera Selection -->
        <div class="control-section">
          <h3>{{ $t('playback.selectCamera') }}</h3>
          <select id="camera-select" class="playback-select" v-model="selectedCamera">
            <option value="">{{ $t('playback.pleaseSelectCamera') }}</option>
            <option v-for="cam in cameras" :key="cam.id" :value="cam.id">
              {{ $t('playback.cameraTitle', { id: cam.id, name: cam.name }) }}
            </option>
          </select>
        </div>

        <!-- Time Selection -->
        <div class="control-section">
          <h3>{{ $t('playback.selectTime') }}</h3>
          <div class="time-controls">
            <div class="date-input">
              <input type="date" id="playback-date" class="playback-input" v-model="selectedDate">
            </div>
            <div class="time-input">
              <input type="time" id="playback-time" class="playback-input" v-model="selectedTime">
            </div>
          </div>
        </div>

        <!-- Playback Controls -->
        <div class="control-section">
          <h3>{{ $t('playback.playbackControls') }}</h3>
          <div class="playback-controls">
            <button id="play-btn" class="playback-btn" @click="startPlayback" :disabled="isPlaying">
              <span class="btn-icon">▶</span> {{ $t('playback.play') }}
            </button>
            <button id="pause-btn" class="playback-btn" @click="pausePlayback" :disabled="!isPlaying">
              <span class="btn-icon">⏸</span> {{ $t('playback.pause') }}
            </button>
            <button id="stop-btn" class="playback-btn" @click="stopPlayback" :disabled="!isPlaying && progress === 0">
              <span class="btn-icon">⏹</span> {{ $t('playback.stop') }}
            </button>
          </div>
          <div class="speed-controls">
            <label>{{ $t('playback.playbackSpeed') }}：</label>
            <select id="speed-select" class="playback-select" v-model="playbackSpeed">
              <option value="0.25">0.25x</option>
              <option value="0.5">0.5x</option>
              <option value="1">1x</option>
              <option value="2">2x</option>
              <option value="4">4x</option>
              <option value="8">8x</option>
            </select>
          </div>
        </div>

        <!-- Timeline -->
        <div class="control-section">
          <h3>{{ $t('playback.timeline') }}</h3>
          <div class="timeline-container">
            <div class="timeline">
              <div class="timeline-track">
                <div class="timeline-progress" id="timeline-progress" :style="{ width: progress + '%' }"></div>
              </div>
              <div class="timeline-markers">
                <div class="timeline-marker" style="left: 20%"><span class="marker-time">09:00</span></div>
                <div class="timeline-marker" style="left: 40%"><span class="marker-time">12:00</span></div>
                <div class="timeline-marker" style="left: 60%"><span class="marker-time">15:00</span></div>
                <div class="timeline-marker" style="left: 80%"><span class="marker-time">18:00</span></div>
              </div>
            </div>
            <div class="timeline-info">
              <span id="current-time">{{ currentTime }}</span>
              <span>/</span>
              <span id="total-time">24:00:00</span>
            </div>
          </div>
        </div>

        <!-- Screenshot and Export -->
        <div class="control-section">
          <h3>{{ $t('playback.operations') }}</h3>
          <div class="action-controls">
            <button id="screenshot-btn" class="playback-btn" @click="takeScreenshot">
              <span class="btn-icon">📷</span> {{ $t('playback.screenshot') }}
            </button>
            <button id="export-btn" class="playback-btn" @click="exportVideo">
              <span class="btn-icon">💾</span> {{ $t('playback.exportClip') }}
            </button>
          </div>
        </div>
      </div>

      <!-- Right Video Panel -->
      <div class="video-panel card">
        <h2>{{ $t('playback.videoPlayback') }}</h2>
        <div class="video-container">
          <div id="playback-video" class="playback-video">
            <div class="video-placeholder">
              <div class="placeholder-icon">{{ placeholderIcon }}</div>
              <div class="placeholder-text">{{ placeholderText }}</div>
            </div>
          </div>
        </div>
        
        <!-- Event Markers -->
        <div class="events-panel">
          <h3>{{ $t('playback.eventMarkers') }}</h3>
          <div class="events-list" id="events-list">
            <div v-for="event in events" :key="event.time" class="event-item">
              <span class="event-time">{{ event.time }}</span>
              <span class="event-type">{{ event.type }}</span>
              <button class="event-jump-btn" @click="jumpToEvent(event.time)">{{ $t('playback.jump') }}</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';

const route = useRoute();
const { t: $t } = useI18n();

// --- Data ---
const cameras = ref([
    { id: 1, name: '工地入口' }, { id: 2, name: '材料堆放区' },
    { id: 3, name: '塔吊作业区' }, { id: 4, name: '东侧围栏' },
    { id: 5, name: '生活区入口' }, { id: 6, name: '深基坑北侧' },
    { id: 7, name: '仓库A' }, { id: 8, name: '临时道路' },
    { id: 9, name: '西侧围栏' }, { id: 10, name: '油气管道区' },
    { id: 11, name: '深基坑南侧' }, { id: 12, name: '大门岗哨' },
    { id: 13, name: '仓库B' }, { id: 14, name: '变电站' },
    { id: 15, name: '搅拌机区域' }, { id: 16, name: '员工通道' },
]);
const events = ref([
    { time: '14:30', type: '施工人员禁区作业' },
    { time: '15:45', type: '工程车辆违规进入' },
    { time: '16:20', type: '施工人员未佩戴安全帽' },
]);

const selectedCamera = ref<number | string>('');
const selectedDate = ref(new Date().toISOString().split('T')[0]);
const selectedTime = ref('12:00');
const playbackSpeed = ref(1);

const isPlaying = ref(false);
const progress = ref(0);
let playbackInterval: number | undefined;

// --- Computed Properties ---
const placeholderIcon = computed(() => isPlaying.value ? '▶️' : '🎬');
const placeholderText = computed(() => {
    if (isPlaying.value) return $t('playback.nowPlaying');
    if (progress.value > 0) return $t('playback.paused');
    return $t('playback.pleaseSelectToStart');
});
const currentTime = computed(() => {
    const totalSeconds = 24 * 60 * 60;
    const currentSeconds = Math.floor((progress.value / 100) * totalSeconds);
    const hours = Math.floor(currentSeconds / 3600).toString().padStart(2, '0');
    const minutes = Math.floor((currentSeconds % 3600) / 60).toString().padStart(2, '0');
    const seconds = (currentSeconds % 60).toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
});

// --- Methods ---
function startPlayback() {
    if (!selectedCamera.value) {
        alert($t('playback.pleaseSelectCameraFirst'));
        return;
    }
    if (!selectedDate.value) {
        alert($t('playback.pleaseSelectDate'));
        return;
    }
    
    isPlaying.value = true;
    clearInterval(playbackInterval);
    playbackInterval = setInterval(() => {
        progress.value += 0.1 * playbackSpeed.value; // Adjust step for smoother progress
        if (progress.value >= 100) {
            stopPlayback();
        }
    }, 100);
}

function pausePlayback() {
    isPlaying.value = false;
    clearInterval(playbackInterval);
}

function stopPlayback() {
    isPlaying.value = false;
    progress.value = 0;
    clearInterval(playbackInterval);
}

function takeScreenshot() {
    if (!isPlaying.value && progress.value === 0) {
        alert($t('playback.pleaseStartPlaybackFirst'));
        return;
    }
    alert($t('playback.screenshotFunction', { filename: `screenshot_${new Date().toISOString().replace(/[:.]/g, '-')}.png` }));
}

function exportVideo() {
    if (!isPlaying.value && progress.value === 0) {
        alert($t('playback.pleaseStartPlaybackFirst'));
        return;
    }
    const startTime = prompt($t('playback.enterExportStartTime'), '00:00:00');
    const endTime = prompt($t('playback.enterExportEndTime'), '00:05:00');
    if (startTime && endTime) {
        alert($t('playback.exportFunction', { startTime, endTime }));
    }
}

function jumpToEvent(eventTime: string) {
    const [hours, minutes] = eventTime.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes;
    const percentage = (totalMinutes / (24 * 60)) * 100;
    progress.value = percentage;
    if (!isPlaying.value) {
        startPlayback();
    }
}

// --- Watchers & Lifecycle ---
watch(playbackSpeed, () => {
    if (isPlaying.value) {
        // Re-start playback to apply new speed
        startPlayback();
    }
});

onMounted(() => {
  const cameraIdFromRoute = route.query.cameraId;
  if (cameraIdFromRoute) {
    const camId = Number(cameraIdFromRoute);
    if (cameras.value.some(c => c.id === camId)) {
      selectedCamera.value = camId;
    }
  }
});
</script>


<style scoped>
/* Using global styles from main.css */
</style>
