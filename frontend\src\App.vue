<script setup lang="ts">
import { onMounted } from 'vue';
import { RouterView } from 'vue-router';
import { useCameraStore } from '@/stores/cameraStore';

// 在应用顶层获取全局数据
const cameraStore = useCameraStore();
onMounted(() => {
  cameraStore.fetchAllData(true); // 传入 true 强制刷新，确保每次应用加载都获取最新数据
});
</script>

<template>
  <RouterView />
</template>

<style scoped>
/* 可以在这里添加全局的、只在App.vue中生效的样式 */
</style>


