/* Global Styles */
* {
    box-sizing: border-box;
}

.sidebar * {
    border-spacing: 0;
    border-collapse: collapse;
}

:root {
    --primary-color: #005a9c; /* Deep blue */
    --secondary-color: #007bff; /* Bright blue */
    --accent-color: #00bfff; /* Bright blue accent */
    --background-color: #f0f4f8; /* Light blue-gray background */
    --light-background: #ffffff;
    --text-color: #333;
    --border-color: #e0e0e0;
    --alarm-color: #d9534f; /* Red for alarms */
    --control-dark-bg: #2a2a2a; /* Dark background for control panel */
    --control-light-bg: #3a3a3a; /* Lighter dark background */
    --control-border: #4a4a4a; /* Border color for controls */
    --control-text: #ffffff; /* White text for controls */
    --control-accent: #00bfff; /* Cyan accent color */
}

html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
}

/* Left Navigation */
.sidebar {
    width: 240px;
    background-color: var(--primary-color);
    color: white;
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
    flex-shrink: 0; /* Prevent sidebar from shrinking */
}

.sidebar-header {
    padding: 0 20px 20px 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h1 {
    margin: 0;
    font-size: 1.5em;
}

.sidebar nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
    border: none;
    background: none;
}

.sidebar nav li {
    margin: 0;
    padding: 0;
    border: none;
    background: none;
    line-height: 1;
}

.sidebar nav li + li {
    margin-top: 0;
    border-top: none;
}

.sidebar nav li a {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: white;
    text-decoration: none;
    font-size: 1.1em;
    transition: background-color 0.3s;
    border-left: 4px solid transparent;
    border-top: none;
    border-right: none;
    border-bottom: none;
    margin: 0;
}

.sidebar nav li a:hover {
    background-color: #004a8c;
}

.sidebar nav li a.active {
    background-color: #004a8c;
    border-left: 4px solid var(--accent-color);
}

.sidebar nav li a .icon {
    margin-right: 15px;
    font-size: 1.2em;
}

/* 下拉菜单样式 */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.dropdown-toggle::after {
    content: '▼';
    font-size: 12px;
    margin-left: auto;
    transition: transform 0.3s ease;
}

.dropdown.active .dropdown-toggle::after {
    transform: rotate(180deg);
}

.dropdown-menu {
    display: none;
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: #2C4770;
    border-left: 3px solid #4A90E2;
    border-right: none;
    border-top: none;
    border-bottom: none;
    line-height: 1;
}

.dropdown-menu li {
    margin: 0;
    padding: 0;
    border: none;
    background: none;
}

.dropdown-menu li + li {
    margin-top: 0;
    border-top: none;
}

.dropdown.active .dropdown-menu {
    display: block;
}

.dropdown-menu li a {
    padding-left: 45px;
    padding-top: 3px;
    padding-bottom: 3px;
    font-size: 0.75em;
    border-left: none;
    border-right: none;
    border-top: none;
    border-bottom: none;
    line-height: 1.1;
    color: #ffffff;
    opacity: 0.9;
    background: none;
    margin: 0;
}

.dropdown-menu li a .icon {
    margin-right: 10px;
    font-size: 0.9em;
    opacity: 0.8;
}

.dropdown-menu li a:hover {
    background-color: #3A5B8D;
    opacity: 1;
}

.dropdown-menu li a.active {
    background-color: #3A5B8D;
    border-left: none;
    opacity: 1;
}

.alarm-bell {
    color: #ffc107;
    animation: bell-shake 1.5s infinite;
}

@keyframes bell-shake {
    0%, 50%, 100% { transform: rotate(0); }
    60%, 80% { transform: rotate(15deg); }
    70%, 90% { transform: rotate(-15deg); }
}


/* Main Content */
.main-content {
    flex-grow: 1; /* Allow main content to grow and fill available space */
    padding: 20px;
    overflow-y: auto; /* Add scroll for long content */
    height: 100vh; /* Make it full height */
}

.container {
    width: 100%;
    margin: 0;
    padding: 0;
}

/* Card Style */
.card {
    background-color: var(--light-background);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.card h2 {
    margin-top: 0;
    color: var(--primary-color);
    border-bottom: 2px solid var(--background-color);
    padding-bottom: 10px;
    font-size: 1.4em;
}

/* Dashboard Page */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    height: calc(100vh - 40px);
}

.map-area, .alarms-area {
    display: flex;
    flex-direction: column;
}

#map-container {
    flex-grow: 1;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

#google-map {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    min-height: 450px;
}

/* Leaflet map container styles */
.leaflet-container {
    width: 100% !important;
    height: 100% !important;
    border-radius: 8px;
}

.leaflet-popup-content {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    color: #333;
}

.custom-div-icon {
    background: none !important;
    border: none !important;
}

/* Camera notification styles */
.camera-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

.notification-content {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    gap: 10px;
    min-width: 300px;
}

.notification-icon {
    font-size: 20px;
    color: #007bff;
}

.notification-text {
    flex: 1;
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    color: #999;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.notification-close:hover {
    background-color: #f0f0f0;
    color: #666;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Playback page styles */
.playback-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 20px;
    height: calc(100vh - 40px);
}

.control-panel {
    height: fit-content;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

.control-section {
    margin-bottom: 25px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
}

.control-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.control-section h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.playback-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.playback-select:focus {
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.playback-input {
    width: 290px;
    padding: 10px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.playback-input:focus {
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.time-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.date-input, .time-input {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.date-input label, .time-input label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.playback-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.playback-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border: 2px solid #1e3a8a;
    background-color: #ffffff;
    color: #1e3a8a;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex: 1;
}

.playback-btn:hover:not(:disabled) {
    background-color: #1e3a8a;
    color: #ffffff;
}

.playback-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: #cbd5e1;
    color: #9ca3af;
}

.btn-icon {
    font-size: 16px;
    font-weight: bold;
}

.speed-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.speed-controls label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.speed-controls select {
    flex: 1;
}

.timeline-container {
    margin-top: 15px;
}

.timeline {
    position: relative;
    height: 60px;
    margin-bottom: 10px;
}

.timeline-track {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 4px;
    background-color: #e0e0e0;
    border-radius: 2px;
    transform: translateY(-50%);
}

.timeline-progress {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: #1e3a8a;
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
}

.timeline-markers {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
}

.timeline-marker {
    position: absolute;
    top: 50%;
    width: 2px;
    height: 12px;
    background-color: #666;
    transform: translateY(-50%);
}

.marker-time {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    color: #666;
    white-space: nowrap;
}

.timeline-info {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.action-controls {
    display: flex;
    gap: 10px;
}

.video-panel {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 40px);
}

.video-container {
    flex: 1;
    margin-bottom: 20px;
    min-height: 500px; /* 增加最小高度 */
}

.playback-video {
    width: 100%;
    height: 100%;
    min-height: 480px; /* 增加最小���度 */
    background-color: #000;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.video-placeholder {
    text-align: center;
    color: #666;
}

.placeholder-icon {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.placeholder-text {
    font-size: 16px;
    color: #999;
}

.events-panel {
    height: 250px; /* 增加事件面板高度 */
    overflow-y: auto;
}

.events-panel h3 {
    margin-bottom: 15px;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.events-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.event-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #1e3a8a;
}

.event-time {
    font-weight: 600;
    color: #1e3a8a;
    min-width: 60px;
}

.event-type {
    flex: 1;
    color: #333;
}

.event-jump-btn {
    padding: 6px 12px;
    background-color: #1e3a8a;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

.event-jump-btn:hover {
    background-color: #1e40af;
}

/* Responsive design for playback page */
@media (max-width: 1200px) {
    .playback-layout {
        grid-template-columns: 300px 1fr;
    }
}

@media (max-width: 768px) {
    .playback-layout {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .control-panel {
        max-height: none;
    }
    
    .playback-controls {
        flex-direction: column;
    }
    
    .action-controls {
        flex-direction: column;
    }
    
    .events-panel {
        height: 150px;
    }
}



.camera-pin {
    position: absolute;
    width: 35px;
    height: 35px;
    background-color: var(--secondary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.5em;
    border: 2px solid white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    transition: transform 0.2s, background-color 0.2s;
    z-index: 10;
    /* display: none; */ /* This was the culprit, removing it */
}
.camera-pin:hover {
    transform: scale(1.2);
}
.camera-pin.alarm {
    background-color: var(--alarm-color);
    animation: pulse 1s infinite;
}
@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(217, 83, 79, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(217, 83, 79, 0); }
    100% { box-shadow: 0 0 0 0 rgba(217, 83, 79, 0); }
}

#cam1 { top: 20%; left: 30%; }
#cam2 { top: 50%; left: 60%; }
#cam3 { top: 70%; left: 25%; }

.alarm-list {
    flex-grow: 1;
    overflow-y: auto;
}
.alarm-list ul { list-style: none; padding: 0; }
.alarm-list li {
    padding: 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}
.alarm-list li:hover { background-color: #f9f9f9; }
.alarm-list .location { font-weight: bold; color: var(--primary-color); }
.alarm-list .event { color: var(--alarm-color); }
.alarm-list .time { color: #888; font-size: 0.9em; float: right; }

/* Chart Area in Dashboard */
.chart-area {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.chart-container {
    position: relative;
    height: 250px; /* Adjust as needed */
}

/* Modal for Alarm Video */
.modal {
    display: flex; /* Changed from none to flex */
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.6);
    align-items: center;
    justify-content: center;
}
.modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    max-width: 900px;
    border-radius: 8px;
    position: relative;
}
.modal-content.large {
    max-width: 1100px;
    max-height: 700px;
    width: 85vw;
    height: 80vh;
}

.modal-body {
    display: flex;
    gap: 30px;
    height: calc(100% - 60px);
    overflow: hidden;
}

.video-detail-container {
    flex: 0.7;
    position: relative;
    max-height: 550px;
}

#detail-modal-image {
    width: 100%;
    max-height: 500px;
    object-fit: cover;
    border-radius: 8px;
}

#drawing-canvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: crosshair;
    max-height: 500px;
    width: 100%;
}

.config-detail-container {
    flex: 1;
    max-width: 400px;
    overflow-y: auto;
    padding-right: 20px;
    max-height: 600px;
    margin-top: -10px;
}

.close-btn {
    color: #aaa;
    position: absolute;
    top: 10px;
    right: 20px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.2s ease;
}

.close-btn:hover {
    color: #333;
}

/* Camera Points Page Layout */
.camera-points-layout {
    display: grid;
    grid-template-columns: 280px 1fr; /* Fixed width for tree, rest for grid */
    gap: 20px;
    align-items: start;
}

.video-panel .card {
    margin-bottom: 0;
}

/* Tree View Styles */
.camera-tree, .camera-tree ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.camera-tree ul {
    padding-left: 20px;
}

.camera-tree .tree-node {
    padding: 8px 10px;
    cursor: pointer;
    border-radius: 5px;
    transition: background-color 0.2s;
    display: block;
}

.camera-tree .tree-node:hover {
    background-color: var(--background-color);
}

.camera-tree .tree-node.active {
    background-color: var(--secondary-color);
    color: white;
}

.camera-tree .tree-parent > .tree-node {
    font-weight: bold;
}

.camera-tree .tree-parent > .tree-node::before {
    content: '▼';
    display: inline-block;
    margin-right: 8px;
    transition: transform 0.2s;
}

.camera-tree .tree-parent.collapsed > .tree-node::before {
    transform: rotate(-90deg);
}

.camera-tree .tree-parent.collapsed > ul {
    display: none;
}

.camera-tree .tree-leaf .tree-node::before {
    content: '📹';
    display: inline-block;
    margin-right: 8px;
}


/* Camera Points Page */
.controls { 
    margin-bottom: 20px; 
    display: flex;
    align-items: center;
    gap: 10px;
}

.controls span {
    font-weight: 500;
    color: #374151;
}

.controls button {
    padding: 6px 12px;
    border: 1px solid #1e3a8a;
    background-color: #ffffff;
    color: #1e3a8a;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.controls button:hover {
    background-color: #1e3a8a;
    color: #ffffff;
}

.controls button.active {
    background-color: #1e3a8a;
    color: #ffffff;
}
.video-grid {
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(4, 1fr); /* Default 16 */
}
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;
}
.pagination-controls button {
    margin: 0 10px;
}
.pagination-controls span {
    font-weight: bold;
}
.video-grid.grid-1 { grid-template-columns: 1fr; }
.video-grid.grid-4 { grid-template-columns: repeat(2, 1fr); }
.video-grid.grid-9 { grid-template-columns: repeat(3, 1fr); }

.video-item {
    position: relative;
    background-color: #000;
    aspect-ratio: 16 / 9;
    cursor: pointer;
}
.video-item::after {
    content: '▶';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 48px;
    color: rgba(255, 255, 255, 0.7);
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: color 0.3s, transform 0.3s;
}
.video-item:hover::after {
    color: rgba(255, 255, 255, 1);
    transform: translate(-50%, -50%) scale(1.1);
}
.video-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.video-item .overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.5);
    color: white;
    padding: 5px;
    font-size: 0.9em;
}

/* Remote Controls in Modal */
.control-header {
    margin-top: 30px;
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
}
.remote-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}
.command-sender {
    display: flex;
    gap: 10px;
}
.command-sender input {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
}
.config-action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}
.config-action-buttons button {
    flex: 1; /* Equal width */
}

.action-buttons {
    display: flex;
    gap: 10px;
}
.action-buttons button {
    flex: 1; /* Equal width */
}
#mic-btn {
    font-size: 1.5em;
    padding: 8px 15px;
}

/* Alarm History Page */
.filters { display: flex; gap: 15px; flex-wrap: wrap; margin-bottom: 20px; }
.filters input, .filters select, .filters button {
    padding: 10px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
}
.history-table { width: 100%; border-collapse: collapse; }
.history-table th, .history-table td {
    border: 1px solid var(--border-color);
    padding: 12px;
    text-align: left;
}
.history-table th { background-color: #f2f2f2; }
.history-table tr:nth-child(even) { background-color: #f9f9f9; }
.history-table tr:hover { background-color: #f1f1f1; cursor: pointer; }
.history-table .status-confirmed { color: green; }
.history-table .status-unconfirmed { color: orange; }
.history-table .status-false { color: gray; }
.history-table img { width: 100px; cursor: pointer; }

/* Alarm History Modal Styles */
.alarm-detail-body {
    display: flex;
    gap: 20px;
    margin: 20px 0;
}
#history-modal-image {
    max-width: 50%;
    border-radius: 8px;
}
.alarm-detail-info p {
    margin: 10px 0;
    font-size: 1.1em;
}
.modal-actions {
    text-align: right;
    margin-top: 20px;
}
.modal-actions button {
    margin-left: 10px;
}
.btn-confirm {
    background-color: #5cb85c; /* Green */
}
.btn-confirm:hover {
    background-color: #4cae4c;
}
.btn-false {
    background-color: #f0ad4e; /* Orange */
}
.btn-false:hover {
    background-color: #eea236;
}


/* Config Page */
.config-form ul {
    list-style: none;
    padding: 0;
    margin-bottom: 20px;
}

.config-form li {
    padding: 10px 0;
    font-size: 1.2em;
    border-bottom: 1px solid #e2e8f0;
}

.config-form li:last-child {
    border-bottom: none;
}

.config-form label {
    display: block;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 500;
    font-size: 18px;
}

.config-form input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
    accent-color: #1e3a8a;
}

.config-action-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.config-action-buttons button {
    background-color: #ffffff;
    color: #1e3a8a;
    border: 2px solid #1e3a8a;
    padding: 10px 16px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    flex: 1;
}

.config-action-buttons button:hover {
    background-color: #1e3a8a;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.2);
}

.config-action-buttons button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(30, 58, 138, 0.2);
}

.control-header {
    margin-top: 25px;
    margin-bottom: 15px;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
}

.remote-controls {
    margin-bottom: 20px;
}

.command-sender {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.command-sender input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #cbd5e1;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.3s ease;
}

.command-sender input:focus {
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.command-sender button {
    background-color: #ffffff;
    color: #1e3a8a;
    border: 2px solid #1e3a8a;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    white-space: nowrap;
}

.command-sender button:hover {
    background-color: #1e3a8a;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.2);
}

.command-sender button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(30, 58, 138, 0.2);
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.action-buttons button {
    background-color: #ffffff;
    color: #1e3a8a;
    border: 2px solid #1e3a8a;
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    flex: 1;
}

.action-buttons button:hover {
    background-color: #1e3a8a;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(30, 58, 138, 0.2);
}

.action-buttons button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(30, 58, 138, 0.2);
}

button {
    background-color: var(--secondary-color);
    color: white;
    border: none;
    padding: 12px 25px;
    font-size: 1em;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}
button:hover { background-color: #0056b3; }

/* Direction Control Pad */
.direction-control {
    display: flex;
    justify-content: center;
}

.direction-pad {
    position: relative;
    width: 180px;
    height: 180px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 4px;
    background-color: var(--control-dark-bg);
    border-radius: 8px;
    padding: 8px;
    border: 1px solid var(--control-border);
}

.direction-btn {
    background-color: #ffffff; /* 白色背景 */
    border: 1px solid #e2e8f0; /* 浅灰色边框 */
    color: #1e3a8a; /* 深蓝色图标 */
    border-radius: 4px; /* 按钮圆角 */
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
    position: relative;
    min-width: 0; /* 移除最小宽度限制 */
    min-height: 0; /* 移除最小高度限制 */
    aspect-ratio: 1; /* 确保按钮为正方形 */
    box-sizing: border-box; /* 确保边框包含在按钮尺寸内 */
    /* 强制正方形 - 兼容性方案 */
    max-width: none;
    max-height: none;
}

.direction-btn:hover {
    background-color: var(--control-accent);
    color: var(--control-dark-bg);
}

.direction-btn:active {
    transform: scale(0.95);
}

.direction-btn.center {
    grid-column: 2;
    grid-row: 2;
    background-color: var(--control-accent);
    color: var(--control-dark-bg);
    border-color: var(--control-accent);
}

/* Parameter Controls */
.parameter-controls {
    display: flex;
    flex-direction: column;
    gap: 10px; /* 稍微调大间距，从8px改为10px */
}

.parameter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0; /* 稍微调大上下内边距，从4px改为5px */
    border-bottom: 1px solid var(--control-border);
}

.parameter-label {
    font-size: 12px; /* 保持字体大小 */
    font-weight: 500;
    color: var(--control-text);
}

.parameter-buttons {
    display: flex;
    gap: 6px; /* 保持按钮间距 */
}

.param-btn {
    background-color: var(--control-light-bg);
    border: 1px solid var(--control-border);
    color: var(--control-text);
    width: 28px; /* 保持按钮尺寸 */
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 14px; /* 保持字体大小 */
    font-weight: bold;
    transition: all 0.2s ease;
}

.param-btn:hover {
    background-color: var(--control-accent);
    color: var(--control-dark-bg);
}

.param-btn:active {
    transform: scale(0.95);
}

/* Speed Control */
.speed-control {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.speed-slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.speed-slider {
    flex: 1;
    height: 6px;
    background-color: var(--control-light-bg);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.speed-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background-color: var(--control-accent);
    border: 2px solid var(--control-accent);
    border-radius: 50%;
    cursor: pointer;
}

.speed-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background-color: var(--control-accent);
    border: 2px solid var(--control-accent);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.speed-value {
    color: var(--control-accent);
    font-weight: bold;
    font-size: 16px;
    min-width: 20px;
    text-align: center;
}

/* Update camera-points-layout to accommodate control panel */
.camera-points-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    gap: 20px;
    height: calc(100vh - 40px);
}

.tree-panel {
    height: fit-content;
}

.video-panel {
    height: fit-content;
}

/* Responsive adjustments */
@media (max-width: 1400px) {
    .camera-points-layout {
        grid-template-columns: 250px 1fr;
    }
}

/* Modal Camera Control Panel Styles */
.camera-control-panel {
    margin-top: 15px;
    background-color: #ffffff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: auto; /* Add this line to ensure the panel fits its content */
}

.control-panel-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 25px;
    background-color: #1e3a8a;
    border-bottom: 1px solid #e0e0e0;
}

.control-icon {
    font-size: 20px;
    margin-right: 12px;
    color: #ffffff;
}

.control-title {
    color: #ffffff;
    font-size: 18px;
    font-weight: 500;
    flex: 1;
}

.control-close-btn {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 18px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.control-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.control-reset-btn {
    background: none;
    border: 1px solid #ffffff;
    color: #ffffff;
    font-size: 12px;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    font-weight: 500;
}

.control-reset-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.8);
}

/* 预置点控制按��样式 */
.preset-controls {
    display: flex;
    gap: 10px;
    margin: 20px 0;
    justify-content: center;
}

.preset-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background-color: #1e3a8a;
    color: #ffffff;
    border: 1px solid #1e3a8a;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.preset-btn:hover {
    background-color: #1e40af;
    border-color: #1e40af;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preset-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.preset-btn .btn-icon {
    font-size: 16px;
}

/* 音量控制样式 */
.volume-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.volume-icon {
    font-size: 18px;
    color: #1e3a8a;
    cursor: pointer;
    transition: color 0.2s ease;
}

.volume-icon:hover {
    color: #1e40af;
}

.volume-slider-container {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.volume-slider {
    flex: 1;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background-color: #1e3a8a;
    border: 2px solid #1e3a8a;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
    background-color: #1e40af;
    border-color: #1e40af;
    transform: scale(1.1);
}

.volume-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background-color: #1e3a8a;
    border: 2px solid #1e3a8a;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.volume-slider::-moz-range-thumb:hover {
    background-color: #1e40af;
    border-color: #1e40af;
    transform: scale(1.1);
}

.volume-value {
    color: #1e3a8a;
    font-weight: 600;
    font-size: 14px;
    min-width: 40px;
    text-align: center;
}

/* 摄像头管理页面样式 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.btn-primary {
    background-color: #1e3a8a;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    background-color: #1e40af;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s ease;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.search-input {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    width: 250px;
}

.filter-select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    background-color: white;
}

.camera-table-container {
    overflow-x: auto;
    margin-top: 20px;
}

.camera-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.camera-table th,
.camera-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

.camera-table th {
    background-color: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.camera-table tr:hover {
    background-color: #f9fafb;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.online {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.offline {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-badge.maintenance {
    background-color: #fef3c7;
    color: #92400e;
}

.btn-edit,
.btn-delete {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    margin-right: 4px;
    transition: all 0.2s ease;
}

.btn-edit {
    background-color: #3b82f6;
    color: white;
}

.btn-edit:hover {
    background-color: #2563eb;
}

.btn-delete {
    background-color: #ef4444;
    color: white;
}

.btn-delete:hover {
    background-color: #dc2626;
}

.camera-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* 工地管理样式 */
.site-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.site-item {
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.site-item:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.site-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.site-title {
    font-size: 18px;
    font-weight: 600;
    color: #1e3a8a;
    margin: 0;
}

.site-actions {
    display: flex;
    gap: 8px;
}

.site-info {
    margin-bottom: 15px;
}

.site-info p {
    margin: 5px 0;
    color: #6b7280;
    font-size: 14px;
}

.site-info .site-area {
    display: inline-block;
    background-color: #dbeafe;
    color: #1e40af;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.site-cameras {
    border-top: 1px solid #e5e7eb;
    padding-top: 15px;
}

.site-cameras h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #374151;
}

.camera-count {
    background-color: #f3f4f6;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    color: #6b7280;
}

.camera-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.site-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.site-form .form-group textarea {
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
}

.site-form .form-group textarea:focus {
    outline: none;
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

/* 模态框头部样式 */
.modal-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e8f0;
}

.title-with-restart {
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-header h2 {
    margin: 0;
    color: #1e3a8a;
    font-size: 20px;
    font-weight: 600;
}

.restart-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background-color: #dc3545;
    color: #ffffff;
    border: 1px solid #dc3545;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.restart-btn:hover {
    background-color: #c82333;
    border-color: #c82333;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.restart-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.restart-btn .btn-icon {
    font-size: 16px;
}

.restart-btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.restart-btn:disabled:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    transform: none;
    box-shadow: none;
}

/* Modal specific camera controls */
.camera-control-panel .camera-controls {
    padding: 22px;
    display: flex;
    flex-direction: column;
    gap: 28px;
    background-color: #ffffff;
}

.camera-control-panel .control-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 40px;
}

.camera-control-panel .direction-control {
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.camera-control-panel .direction-pad {
    width: 140px;
    height: 140px;
    gap: 4px;
    background-color: #f1f5f9;
    border-radius: 8px;
    padding: 8px;
    border: 1px solid #e2e8f0;
}

.camera-control-panel .direction-btn {
    background-color: #ffffff; /* 白色背景 */
    border: 1px solid #e2e8f0; /* 浅灰色边框 */
    color: #1e3a8a; /* 深蓝色图标 */
    border-radius: 4px; /* 按钮圆角 */
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
    position: relative;
    min-width: 0; /* 移除最小宽度限制 */
    min-height: 0; /* 移除最小高度限制 */
    aspect-ratio: 1; /* 确保按钮为正方形 */
    box-sizing: border-box; /* 确保边框包含在按钮尺寸内 */
}

.camera-control-panel .direction-btn:hover {
    background-color: #1e3a8a;
    color: #ffffff;
    border-color: #1e3a8a;
}

.camera-control-panel .direction-btn:active {
    transform: scale(0.95);
}

.camera-control-panel .direction-btn.center {
    background-color: #1e3a8a;
    color: #ffffff;
    border-color: #1e3a8a;
}

.camera-control-panel .parameter-controls {
    display: flex;
    flex-direction: column;
    gap: 10px; /* 稍微调大间距，从8px改为10px */
    flex: 1;
}

.camera-control-panel .parameter-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 5px; /* 稍微调大下内边距，从4px改为5px */
    border-bottom: 1px solid #e2e8f0;
}

.camera-control-panel .parameter-label {
    font-size: 12px; /* 保持字体大小 */
    font-weight: 500;
    color: #374151;
}

.camera-control-panel .parameter-buttons {
    display: flex;
    gap: 6px; /* 保持按钮间距 */
}

.camera-control-panel .param-btn {
    background-color: #ffffff;
    border: 1px solid #e2e8f0;
    color: #374151;
    width: 26px; /* 保持按钮尺寸 */
    height: 26px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.2s ease;
}

.camera-control-panel .param-btn:hover {
    background-color: #1e3a8a;
    color: #ffffff;
    border-color: #1e3a8a;
}

.camera-control-panel .param-btn:active {
    transform: scale(0.95);
}

.camera-control-panel .speed-control {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.camera-control-panel .speed-slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.camera-control-panel .speed-slider {
    flex: 1;
    height: 6px;
    background-color: #e2e8f0;
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    cursor: pointer;
}

.camera-control-panel .speed-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background-color: #1e3a8a;
    border: 2px solid #1e3a8a;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.camera-control-panel .speed-slider::-webkit-slider-thumb:hover {
    background-color: #1e40af;
    border-color: #1e40af;
    transform: scale(1.1);
}

.camera-control-panel .speed-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background-color: #1e3a8a;
    border: 2px solid #1e3a8a;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    transition: all 0.2s ease;
}

.camera-control-panel .speed-slider::-moz-range-thumb:hover {
    background-color: #1e40af;
    border-color: #1e40af;
    transform: scale(1.1);
}

.camera-control-panel .speed-value {
    color: #1e3a8a;
    font-weight: 600;
    font-size: 14px;
    min-width: 20px;
    text-align: center;
}

/* 摄像头配置表单样式 */
.config-form {
    margin-top: 20px;
}

.config-form ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.config-form li {
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.config-form li:last-child {
    border-bottom: none;
}

.config-form label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
}

.config-form input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #1e3a8a;
}

.config-form input[type="text"],
.config-form select,
.config-form textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    margin-top: 8px;
}

.config-form input[type="text"]:focus,
.config-form select:focus,
.config-form textarea:focus {
    outline: none;
    border-color: #1e3a8a;
    box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.storage-path-input {
    width: calc(100% - 200px); /* 调整宽度以适应提示 */
}

.field-hint {
    font-size: 12px;
    color: #6b7280;
    margin-left: 10px;
}

.button-container {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: flex-end;
}

.email-content-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e2e8f0;
}

.email-content-section h3 {
    margin-bottom: 15px;
    color: #374151;
    font-size: 16px;
    font-weight: 600;
}

.email-template {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
}

.email-header,
.email-body {
    margin-bottom: 15px;
}

.email-header label,
.email-body label {
    font-weight: 500;
    color: #374151;
    display: block;
    margin-bottom: 8px;
}

.email-subject-input,
.email-content-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
}

.email-content-textarea {
    resize: vertical;
    min-height: 200px;
    font-family: monospace;
}

/* 视频控制按钮 */
.video-control-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
}

.video-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.record-btn {
    background-color: #dc3545;
    color: white;
    border: 1px solid #dc3545;
}

.record-btn:hover {
    background-color: #c82333;
    border-color: #c82333;
}

.record-btn.recording {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.stop-btn {
    background-color: #007bff;
    color: white;
    border: 1px solid #007bff;
}

.stop-btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.stop-btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.playback-btn {
    background-color: #28a745;
    color: white;
    border: 1px solid #28a745;
}

.playback-btn:hover {
    background-color: #218838;
    border-color: #218838;
}

/* 视频控制按钮 */
.video-control-buttons {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    justify-content: center;
}

.video-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    color: white;
    border: 1px solid;
}

.record-btn {
    background-color: #dc3545;
    border-color: #dc3545;
}

.record-btn:hover {
    background-color: #c82333;
    border-color: #c82333;
}

.record-btn.recording {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.stop-btn {
    background-color: #007bff;
    border-color: #007bff;
}

.stop-btn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.stop-btn:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.playback-btn {
    background-color: #28a745;
    border-color: #28a745;
}

.playback-btn:hover {
    background-color: #218838;
    border-color: #218838;
}

/* 确认删除模态框 */
.delete-warning {
    color: #dc3545;
    font-weight: bold;
    margin-top: 15px;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 20px;
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #e0f2fe;
    color: #0c4a6e;
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 28px;
    font-weight: 700;
    color: #1e3a8a;
}

.stat-label {
    font-size: 14px;
    color: #6b7280;
}

/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h1 {
    font-size: 24px;
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 12px;
}

.btn-icon {
    font-size: 16px;
}

.btn-secondary[disabled] {
    background-color: #d1d5db;
    cursor: not-allowed;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

.direction-pad .direction-btn {
    background-color: #ffffff !important; /* 白色背景，使用!important覆盖全局样式 */
    border: 1px solid #e2e8f0 !important; /* 浅灰色边框 */
    color: #1e3a8a !important; /* 深蓝色图标 */
    border-radius: 4px !important; /* 按钮圆角 */
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    font-size: 16px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    min-width: 0 !important; /* 移除最小宽度限制 */
    min-height: 0 !important; /* 移除最小高度限制 */
    aspect-ratio: 1 !important; /* 确保按钮为正方形 */
    box-sizing: border-box !important; /* 确保边框包含在按钮尺寸内 */
    padding: 0 !important; /* 移除全局padding */
    /* 强制正方形 - 兼容性方案 */
    max-width: none !important;
    max-height: none !important;
}

.direction-pad .direction-btn:hover {
    background-color: #1e3a8a !important; /* 悬停时变为深蓝色背景 */
    color: #ffffff !important; /* 悬停时图标变为白色 */
    border-color: #1e3a8a !important;
    /* 移除可能导致DOM创建的transform和box-shadow */
}

.direction-pad .direction-btn:active {
    transform: scale(0.95) !important;
}

.direction-pad .direction-btn.center {
    grid-column: 2;
    grid-row: 2;
    background-color: #1e3a8a !important; /* 中心按钮深蓝色背景 */
    color: #ffffff !important; /* 中心按钮白色图标 */
    border-color: #1e3a8a !important;
}

.camera-control-panel .direction-pad .direction-btn {
    background-color: #ffffff !important; /* 白色背景 */
    border: 1px solid #e2e8f0 !important; /* 浅灰色边框 */
    color: #1e3a8a !important; /* 深蓝色图标 */
    border-radius: 4px !important; /* 按钮圆角 */
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    font-size: 16px !important;
    transition: all 0.2s ease !important;
    position: relative !important;
    min-width: 0 !important; /* 移除最小宽度限制 */
    min-height: 0 !important; /* 移除最小高度限制 */
    aspect-ratio: 1 !important; /* 确保按钮为正方形 */
    box-sizing: border-box !important; /* 确保边框包含在按钮尺寸内 */
    padding: 0 !important; /* 移除全局padding */
}

.camera-control-panel .direction-pad .direction-btn:hover {
    background-color: #1e3a8a !important; /* 悬停时变为深蓝色背景 */
    color: #ffffff !important; /* 悬停时图标变为白色 */
    border-color: #1e3a8a !important;
    /* 移除可能导致DOM创建的transform和box-shadow */
}

.camera-control-panel .direction-pad .direction-btn.center {
    background-color: #1e3a8a !important; /* 中心按钮深蓝色背景 */
    color: #ffffff !important; /* 中心按钮白色图标 */
    border-color: #1e3a8a !important;
}
