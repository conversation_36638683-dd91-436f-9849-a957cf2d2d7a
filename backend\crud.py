from sqlalchemy.orm import Session
from datetime import datetime, date
from sqlalchemy import func
from sqlalchemy.orm import Session
try:
    from . import models, schemas
except ImportError:
    import models, schemas
from typing import List, Optional, Dict

# ====================
#       Sites
# ====================

def get_site(db: Session, site_id: int):
    return db.query(models.Site).filter(models.Site.id == site_id).first()

def get_site_by_name(db: Session, name: str):
    return db.query(models.Site).filter(models.Site.name == name).first()

def get_sites(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Site).offset(skip).limit(limit).all()

def create_site(db: Session, site: schemas.SiteCreate):
    db_site = models.Site(**site.model_dump())
    db.add(db_site)
    db.commit()
    db.refresh(db_site)
    return db_site

def update_site(db: Session, site_id: int, site: schemas.SiteCreate) -> Optional[models.Site]:
    """Updates a site's information."""
    db_site = get_site(db, site_id=site_id)
    if not db_site:
        return None

    update_data = site.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_site, key, value)

    db.commit()
    db.refresh(db_site)
    return db_site

def delete_site(db: Session, site_id: int) -> Optional[models.Site]:
    """Deletes a site from the database and returns the deleted object."""
    db_site = get_site(db, site_id)
    if db_site:
        db.delete(db_site)
        db.commit()
    return db_site

# ====================
#      Cameras
# ====================

def get_camera(db: Session, camera_id: int):
    return db.query(models.Camera).filter(models.Camera.id == camera_id).first()

def get_cameras(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Camera).offset(skip).limit(limit).all()

def create_camera(db: Session, camera: schemas.CameraCreate) -> models.Camera:
    """创建新摄像头，并根据输入动态生成RTSP地址。"""
    # 检查IP和端口组合是否已存在
    existing_camera = db.query(models.Camera).filter(
        models.Camera.ip_address == camera.ip_address,
        models.Camera.port == camera.port
    ).first()
    if existing_camera:
        raise ValueError(f"IP地址 {camera.ip_address}:{camera.port} 已被使用")
    
    # 动态构造 RTSP URL
    auth_part = ""
    if camera.username and camera.password:
        auth_part = f"{camera.username}:{camera.password}@"
    
    path_part = camera.stream_path if camera.stream_path else "/"
    if not path_part.startswith("/"):
        path_part = "/" + path_part

    rtsp_url = f"rtsp://{auth_part}{camera.ip_address}:{camera.port}{path_part}"
    
    camera_data = camera.model_dump()
    db_camera = models.Camera(**camera_data, rtsp_url=rtsp_url)
    
    db.add(db_camera)
    db.commit()
    db.refresh(db_camera)
    return db_camera

def update_camera(db: Session, camera_id: int, camera: schemas.CameraUpdate) -> Optional[models.Camera]:
    """Updates a camera's information."""
    db_camera = get_camera(db, camera_id=camera_id)
    if not db_camera:
        return None

    update_data = camera.model_dump(exclude_unset=True)

    # Update fields
    for key, value in update_data.items():
        setattr(db_camera, key, value)

    db.commit()
    db.refresh(db_camera)
    return db_camera

def delete_camera(db: Session, camera_id: int) -> Optional[models.Camera]:
    """Deletes a camera from the database and returns the deleted object."""
    db_camera = get_camera(db, camera_id)
    if db_camera:
        db.delete(db_camera)
        db.commit()
    return db_camera

def update_camera_status(db: Session, camera_id: int, status: models.CameraStatusEnum):
    """Updates a camera's status."""
    db_camera = get_camera(db, camera_id)
    if db_camera:
        db_camera.status = status
        db.commit()
        db.refresh(db_camera)
    return db_camera

def update_camera_last_seen(db: Session, camera_id: int, last_seen):
    """Updates a camera's last seen timestamp."""
    db_camera = get_camera(db, camera_id)
    if db_camera:
        db_camera.last_online = last_seen  # 使用数据库中的实际列名
        db.commit()
        db.refresh(db_camera)
    return db_camera

# ====================
#      Alerts
# ====================

def get_alert(db: Session, alert_id: int):
    return db.query(models.Alert).filter(models.Alert.id == alert_id).first()

def get_alerts(db: Session, skip: int = 0, limit: int = 100):
    return db.query(models.Alert).offset(skip).limit(limit).all()

def create_alert(db: Session, alert: schemas.AlertCreate):
    db_alert = models.Alert(**alert.model_dump())
    db.add(db_alert)
    db.commit()
    db.refresh(db_alert)
    return db_alert

# ====================
#   Camera Presets
# ====================

def get_camera_presets(db: Session, camera_id: int) -> List[models.CameraPreset]:
    """获取指定摄像头的所有预置点"""
    return db.query(models.CameraPreset).filter(models.CameraPreset.camera_id == camera_id).all()

def get_camera_preset(db: Session, preset_id: int) -> Optional[models.CameraPreset]:
    """根据ID获取单个预置点"""
    return db.query(models.CameraPreset).filter(models.CameraPreset.id == preset_id).first()

def get_camera_preset_by_name(db: Session, camera_id: int, name: str) -> Optional[models.CameraPreset]:
    """根据摄像头ID和名称获取预置点"""
    return db.query(models.CameraPreset).filter(
        models.CameraPreset.camera_id == camera_id,
        models.CameraPreset.name == name
    ).first()

def create_camera_preset(db: Session, preset: schemas.CameraPresetCreate) -> models.CameraPreset:
    """创建新的摄像头预置点"""
    # 检查同一摄像头下是否已存在同名预置点
    existing_preset = get_camera_preset_by_name(db, preset.camera_id, preset.name)
    if existing_preset:
        raise ValueError(f"摄像头 {preset.camera_id} 下已存在名为 '{preset.name}' 的预置点")
    
    # 检查摄像头是否存在
    camera = get_camera(db, preset.camera_id)
    if not camera:
        raise ValueError(f"摄像头 {preset.camera_id} 不存在")
    
    db_preset = models.CameraPreset(**preset.model_dump())
    db.add(db_preset)
    db.commit()
    db.refresh(db_preset)
    return db_preset

def update_camera_preset(db: Session, preset_id: int, preset: schemas.CameraPresetUpdate) -> Optional[models.CameraPreset]:
    """更新摄像头预置点"""
    db_preset = get_camera_preset(db, preset_id)
    if not db_preset:
        return None
    
    # 如果更新名称，检查是否与同一摄像头下的其他预置点重名
    if preset.name and preset.name != db_preset.name:
        existing_preset = get_camera_preset_by_name(db, db_preset.camera_id, preset.name)
        if existing_preset and existing_preset.id != preset_id:
            raise ValueError(f"摄像头 {db_preset.camera_id} 下已存在名为 '{preset.name}' 的预置点")
    
    update_data = preset.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_preset, field, value)
    
    db.commit()
    db.refresh(db_preset)
    return db_preset

def delete_camera_preset(db: Session, preset_id: int) -> Optional[models.CameraPreset]:
    """删除摄像头预置点"""
    db_preset = get_camera_preset(db, preset_id)
    if db_preset:
        db.delete(db_preset)
        db.commit()
    return db_preset

# 添加Alarm相关的CRUD操作
def get_alarms(db: Session, skip: int = 0, limit: int = 100) -> List[models.Alarm]:
    """获取报警记录列表"""
    return db.query(models.Alarm).offset(skip).limit(limit).all()

def get_alarm(db: Session, alarm_id: int) -> Optional[models.Alarm]:
    """根据ID获取单个报警记录"""
    return db.query(models.Alarm).filter(models.Alarm.id == alarm_id).first()

def create_alarm(db: Session, alarm: schemas.AlarmCreate) -> models.Alarm:
    """创建新的报警记录"""
    db_alarm = models.Alarm(**alarm.model_dump())
    db.add(db_alarm)
    db.commit()
    db.refresh(db_alarm)
    return db_alarm

def update_alarm(db: Session, alarm_id: int, alarm: schemas.AlarmUpdate) -> Optional[models.Alarm]:
    """更新报警记录"""
    db_alarm = db.query(models.Alarm).filter(models.Alarm.id == alarm_id).first()
    if db_alarm is None:
        return None
    
    update_data = alarm.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_alarm, field, value)
    
    db.commit()
    db.refresh(db_alarm)
    return db_alarm

def delete_alarm(db: Session, alarm_id: int) -> bool:
    """删除报警记录"""
    db_alarm = db.query(models.Alarm).filter(models.Alarm.id == alarm_id).first()
    if db_alarm is None:
        return False
    
    db.delete(db_alarm)
    db.commit()
    return True

def get_today_alarms_stats_by_type(db: Session) -> Dict[str, int]:
    """
    获取今日报警的统计数据，按类型分组。
    """
    today_start = datetime.combine(date.today(), datetime.min.time())
    
    # 查询并分组计数
    results = db.query(
        models.Alarm.type,
        func.count(models.Alarm.id)
    ).filter(
        models.Alarm.created_at >= today_start
    ).group_by(
        models.Alarm.type
    ).all()
    
    # 将结果转换为字典
    stats_dict = {alarm_type: count for alarm_type, count in results}
    return stats_dict