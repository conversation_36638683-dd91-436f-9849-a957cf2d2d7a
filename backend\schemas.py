from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime
from decimal import Decimal

# --- PTZ Schemas ---
class PtzCommand(BaseModel):
    """PTZ command sent from the frontend."""
    command: str = Field(..., description="The PTZ command to execute (e.g., 'TILT_UP', 'STOP').")
    speed: int = Field(default=50, ge=1, le=100, description="Movement speed from 1 to 100.")


# --- Site Schemas ---
class SiteBase(BaseModel):
    name: str = Field(..., description="工地名称")
    area: Optional[str] = Field(None, description="地理区域")  # 匹配数据库字段
    description: Optional[str] = Field(None, description="工地描述")  # 匹配数据库字段

class SiteCreate(SiteBase):
    pass

class Site(SiteBase):
    id: int

    class Config:
        from_attributes = True


# --- Camera Schemas ---
class CameraBase(BaseModel):
    name: str = Field(..., description="摄像头名称")
    location: Optional[str] = Field(None, description="摄像头物理位置")
    ip_address: str = Field(..., description="摄像头IP地址")
    port: int = Field(..., description="端口号")
    username: Optional[str] = Field(None, description="认证用户名")
    password: Optional[str] = Field(None, description="认证密码")
    stream_path: Optional[str] = Field(None, description="流路径, e.g., /Streaming/Channels/101")
    rtsp_url: Optional[str] = Field(None, description="RTSP拉流地址")
    latitude: Optional[float] = Field(None, description="纬度坐标")
    longitude: Optional[float] = Field(None, description="经度坐标")
    site_id: Optional[int] = Field(None, description="所属工地ID")

class CameraCreate(BaseModel):
    name: str = Field(..., description="摄像头名称")
    location: Optional[str] = Field(None, description="摄像头物理位置")
    ip_address: str = Field(..., description="摄像头IP地址")
    port: int = Field(..., description="端口号")
    username: Optional[str] = None
    password: Optional[str] = None
    stream_path: Optional[str] = None
    latitude: Optional[float] = Field(None, description="纬度坐标")
    longitude: Optional[float] = Field(None, description="经度坐标")
    site_id: int = Field(..., description="所属工地ID（必需）")

class CameraUpdate(BaseModel):
    name: Optional[str] = None
    location: Optional[str] = None
    ip_address: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    stream_path: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    site_id: Optional[int] = None
    status: Optional[str] = None

class Camera(CameraBase):
    id: int
    status: str = Field(description="摄像头状态 (e.g., '在线', '离线')")
    last_online: Optional[datetime] = Field(None, description="最后在线时间")

    class Config:
        from_attributes = True


# --- Camera Preset Schemas ---
class CameraPresetBase(BaseModel):
    name: str = Field(..., description="预置点名称")
    pan: float = Field(..., ge=-180, le=180, description="水平旋转角度 (-180 to 180)")
    tilt: float = Field(..., ge=-90, le=90, description="垂直倾斜角度 (-90 to 90)")
    zoom: float = Field(default=1.0, ge=1.0, le=100.0, description="缩放级别 (1.0 to 100.0)")

class CameraPresetCreate(CameraPresetBase):
    camera_id: int = Field(..., description="所属摄像头ID")

class CameraPresetUpdate(BaseModel):
    name: Optional[str] = None
    pan: Optional[float] = Field(None, ge=-180, le=180)
    tilt: Optional[float] = Field(None, ge=-90, le=90)
    zoom: Optional[float] = Field(None, ge=1.0, le=100.0)

class CameraPreset(CameraPresetBase):
    id: int
    camera_id: int
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# --- Alert Schemas ---
class AlertBase(BaseModel):
    timestamp: str = Field(..., description="警报发生时间")
    alert_type: str = Field(..., description="警报类型 (e.g., '越界', '安全帽检测')")
    image_url: Optional[str] = Field(None, description="关联截图URL")

class AlertCreate(AlertBase):
    camera_id: int

class Alert(AlertBase):
    id: int
    camera_id: int

    class Config:
        from_attributes = True

# 添加Alarm相关的数据模型
class AlarmBase(BaseModel):
    time: str
    type: str
    location: str
    screenshot: Optional[str] = None
    status: str = "未确认"
    handler: str = "-"

class AlarmCreate(AlarmBase):
    pass

class AlarmUpdate(BaseModel):
    time: Optional[str] = None
    type: Optional[str] = None
    location: Optional[str] = None
    screenshot: Optional[str] = None
    status: Optional[str] = None
    handler: Optional[str] = None

class Alarm(AlarmBase):
    id: int
    created_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# --- Stream Schemas ---
class StreamURL(BaseModel):
    url: str = Field(..., description="The HLS playlist URL for the live stream.")

# Pydantic V2 automatically handles forward references, so manual calls to
# update_forward_refs are no longer needed.
# Site.update_forward_refs(Camera=Camera)
# Camera.update_forward_refs(Alert=Alert)
